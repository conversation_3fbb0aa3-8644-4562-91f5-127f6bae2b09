# 数据源配置
spring:
    datasource:
        type: com.alibaba.druid.pool.DruidDataSource
        druid:
            # 主库数据源
            master:
                url: ***********************************************************************************************************************************************************
                username: hl
                password: hl1234!@#$
            datasource0:
                url: ***********************************************************************************************************************************************************
                username: hl
                password: hl1234!@#$
            datasource1:
                enabled: true
                url: ***********************************************************************************************************************************************************
                username: hl
                password: hl1234!@#$
            datasource2:
                enabled: true
                url: ****************************************************************************************************************************************************
                username: hl
                password: hl1234!@#$
            datasource3:
                enabled: true
                url: ***********************************************************************************************************************************************************
                username: hl
                password: hl1234!@#$
            # 初始连接数
            initialSize: 5
            # 最小连接池数量
            minIdle: 10
            # 最大连接池数量
            maxActive: 20
            # 配置获取连接等待超时的时间
            maxWait: 60000
            # 配置连接超时时间
            connectTimeout: 30000
            # 配置网络超时时间
            socketTimeout: 60000
            # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
            timeBetweenEvictionRunsMillis: 60000
            # 配置一个连接在池中最小生存的时间，单位是毫秒
            minEvictableIdleTimeMillis: 300000
            # 配置一个连接在池中最大生存的时间，单位是毫秒
            maxEvictableIdleTimeMillis: 900000
            # 配置检测连接是否有效
            validationQuery: SELECT 1
            testWhileIdle: true
            testOnBorrow: false
            testOnReturn: false
            statViewServlet:
                # 是否开启 StatViewServlet
                enabled: true
                # 访问监控页面 白名单，默认127.0.0.1
                allow: 127.0.0.1,192.168.10.1/24
                # 访问监控页面 黑名单
#                deny: 192.168.10.1/24
                url-pattern: /druid/*
                # 访问监控页面 登陆账号
                login-username: test
                # 访问监控页面 登陆密码
                login-password: hl123

            webStatFilter: 
                enabled: true
#                # 访问监控页面 白名单，默认127.0.0.1
##                allow: 127.0.0.1
                # 访问监控页面 登陆账号
                login-username: test
                # 访问监控页面 登陆密码
                login-password: hl123
            filter:
                stat:
                    enabled: true
                    # 慢SQL记录
                    log-slow-sql: true
                    slow-sql-millis: 1000
                    merge-sql: true
                wall:
                    config:
                        multi-statement-allow: true
