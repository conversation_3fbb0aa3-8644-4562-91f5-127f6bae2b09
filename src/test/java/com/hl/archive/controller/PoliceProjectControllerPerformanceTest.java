package com.hl.archive.controller;

import com.hl.archive.domain.request.PoliceBaseQueryRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicLong;

/**
 * PoliceProjectController 性能测试
 * 用于验证并发优化的效果
 */
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
public class PoliceProjectControllerPerformanceTest {

    @Autowired
    private PoliceProjectController policeProjectController;

    /**
     * 单次查询性能测试
     */
    @Test
    public void testSingleQueryPerformance() {
        PoliceBaseQueryRequest request = new PoliceBaseQueryRequest();
        request.setIdCard("测试身份证号"); // 替换为实际的测试数据

        // 预热
        for (int i = 0; i < 5; i++) {
            policeProjectController.queryProject(request);
        }

        // 性能测试
        int testCount = 100;
        long totalTime = 0;

        for (int i = 0; i < testCount; i++) {
            long startTime = System.currentTimeMillis();
            policeProjectController.queryProject(request);
            long endTime = System.currentTimeMillis();
            totalTime += (endTime - startTime);
        }

        double avgTime = (double) totalTime / testCount;
        log.info("单次查询平均耗时: {}ms", avgTime);
    }

    /**
     * 并发查询性能测试
     */
    @Test
    public void testConcurrentQueryPerformance() throws InterruptedException {
        PoliceBaseQueryRequest request = new PoliceBaseQueryRequest();
        request.setIdCard("测试身份证号"); // 替换为实际的测试数据

        int threadCount = 10;
        int requestsPerThread = 10;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicLong totalTime = new AtomicLong(0);
        AtomicLong successCount = new AtomicLong(0);
        AtomicLong errorCount = new AtomicLong(0);

        long testStartTime = System.currentTimeMillis();

        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < requestsPerThread; j++) {
                        long startTime = System.currentTimeMillis();
                        try {
                            policeProjectController.queryProject(request);
                            successCount.incrementAndGet();
                        } catch (Exception e) {
                            errorCount.incrementAndGet();
                            log.error("查询失败", e);
                        }
                        long endTime = System.currentTimeMillis();
                        totalTime.addAndGet(endTime - startTime);
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();

        long testEndTime = System.currentTimeMillis();
        long totalTestTime = testEndTime - testStartTime;
        int totalRequests = threadCount * requestsPerThread;

        log.info("并发测试结果:");
        log.info("总请求数: {}", totalRequests);
        log.info("成功请求数: {}", successCount.get());
        log.info("失败请求数: {}", errorCount.get());
        log.info("总测试时间: {}ms", totalTestTime);
        log.info("平均单次耗时: {}ms", (double) totalTime.get() / successCount.get());
        log.info("QPS: {}", (double) successCount.get() * 1000 / totalTestTime);
    }

    /**
     * 压力测试
     */
    @Test
    public void testStressQuery() throws InterruptedException {
        PoliceBaseQueryRequest request = new PoliceBaseQueryRequest();
        request.setIdCard("测试身份证号"); // 替换为实际的测试数据

        int threadCount = 50;
        int requestsPerThread = 20;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        AtomicLong successCount = new AtomicLong(0);
        AtomicLong errorCount = new AtomicLong(0);
        AtomicLong timeoutCount = new AtomicLong(0);

        long testStartTime = System.currentTimeMillis();

        for (int i = 0; i < threadCount; i++) {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < requestsPerThread; j++) {
                        try {
                            long startTime = System.currentTimeMillis();
                            policeProjectController.queryProject(request);
                            long endTime = System.currentTimeMillis();
                            
                            if (endTime - startTime > 5000) { // 超过5秒认为是超时
                                timeoutCount.incrementAndGet();
                            }
                            successCount.incrementAndGet();
                        } catch (Exception e) {
                            errorCount.incrementAndGet();
                            log.error("查询失败", e);
                        }
                    }
                } finally {
                    latch.countDown();
                }
            });
        }

        latch.await();
        executor.shutdown();

        long testEndTime = System.currentTimeMillis();
        long totalTestTime = testEndTime - testStartTime;
        int totalRequests = threadCount * requestsPerThread;

        log.info("压力测试结果:");
        log.info("总请求数: {}", totalRequests);
        log.info("成功请求数: {}", successCount.get());
        log.info("失败请求数: {}", errorCount.get());
        log.info("超时请求数: {}", timeoutCount.get());
        log.info("成功率: {}%", (double) successCount.get() * 100 / totalRequests);
        log.info("总测试时间: {}ms", totalTestTime);
        log.info("QPS: {}", (double) successCount.get() * 1000 / totalTestTime);
    }
}
