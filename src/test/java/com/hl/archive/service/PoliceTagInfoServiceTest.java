package com.hl.archive.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.CombinedTagStatisticsDTO;
import com.hl.archive.domain.dto.TagStatisticsDTO;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.archive.domain.entity.PoliceTagInfo;
import com.hl.archive.domain.request.TagDrillDownRequest;
import com.hl.archive.domain.request.TagStatisticsRequest;
import com.hl.archive.enums.TagTypeEnum;
import com.hl.archive.mapper.PoliceTagInfoMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 标签统计服务测试类
 */
@ExtendWith(MockitoExtension.class)
class PoliceTagInfoServiceTest {

    @Mock
    private PoliceTagInfoMapper policeTagInfoMapper;

    @InjectMocks
    private PoliceTagInfoService policeTagInfoService;

    @Test
    void testGetTagStatistics_EmptyData() {
        // 准备测试数据
        TagStatisticsRequest request = new TagStatisticsRequest();
        request.setTagType(TagTypeEnum.DENGFENG_TRAINING.getCode());
        
        // Mock返回空列表
        when(policeTagInfoMapper.getTagInfoByType(any())).thenReturn(new ArrayList<>());
        
        // 执行测试
        TagStatisticsDTO result = policeTagInfoService.getTagStatistics(request);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(TagTypeEnum.DENGFENG_TRAINING.getCode(), result.getTagType());
        assertEquals(TagTypeEnum.DENGFENG_TRAINING.getName(), result.getTagTypeName());
        assertEquals(0, result.getTotalCount());
        assertNotNull(result.getTagDetails());
        assertTrue(result.getTagDetails().isEmpty());
    }

    @Test
    void testGetTagStatistics_WithData() {
        // 准备测试数据
        TagStatisticsRequest request = new TagStatisticsRequest();
        request.setTagType(TagTypeEnum.DENGFENG_TRAINING.getCode());
        
        // 创建模拟数据
        PoliceTagInfo tagInfo1 = new PoliceTagInfo();
        tagInfo1.setIdCards(Arrays.asList("110101199001011234", "110101199001011235"));
        tagInfo1.setTagName(Arrays.asList("基础训练", "高级训练"));
        
        PoliceTagInfo tagInfo2 = new PoliceTagInfo();
        tagInfo2.setIdCards(Arrays.asList("110101199001011236"));
        tagInfo2.setTagName(Arrays.asList("基础训练"));
        
        List<PoliceTagInfo> mockData = Arrays.asList(tagInfo1, tagInfo2);
        
        // Mock返回数据
        when(policeTagInfoMapper.getTagInfoByType(any())).thenReturn(mockData);
        
        // 执行测试
        TagStatisticsDTO result = policeTagInfoService.getTagStatistics(request);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(TagTypeEnum.DENGFENG_TRAINING.getCode(), result.getTagType());
        assertEquals(TagTypeEnum.DENGFENG_TRAINING.getName(), result.getTagTypeName());
        assertEquals(3, result.getTotalCount()); // 3个不同的人员
        assertNotNull(result.getTagDetails());
        assertEquals(2, result.getTagDetails().size()); // 2种不同的标签
        
        // 验证标签详情（按人数降序排列）
        TagStatisticsDTO.TagDetailStatistics firstDetail = result.getTagDetails().get(0);
        assertEquals("基础训练", firstDetail.getTagName());
        assertEquals(3, firstDetail.getCount()); // 基础训练有3个人
        
        TagStatisticsDTO.TagDetailStatistics secondDetail = result.getTagDetails().get(1);
        assertEquals("高级训练", secondDetail.getTagName());
        assertEquals(2, secondDetail.getCount()); // 高级训练有2个人
    }

    @Test
    void testGetCombinedTagStatistics() {
        // 准备测试数据
        TagStatisticsRequest request = new TagStatisticsRequest();
        request.setOrganizationId("12345678");
        
        // Mock返回空数据（简化测试）
        when(policeTagInfoMapper.getTagInfoByType(any())).thenReturn(new ArrayList<>());
        
        // 执行测试
        CombinedTagStatisticsDTO result = policeTagInfoService.getCombinedTagStatistics(request);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getDengfengTraining());
        assertNotNull(result.getCombatAbility());
        
        assertEquals(TagTypeEnum.DENGFENG_TRAINING.getCode(), result.getDengfengTraining().getTagType());
        assertEquals(TagTypeEnum.COMBAT_ABILITY.getCode(), result.getCombatAbility().getTagType());
        
        assertEquals(0, result.getDengfengTraining().getTotalCount());
        assertEquals(0, result.getCombatAbility().getTotalCount());
    }

    @Test
    void testGetPoliceListByTagType_EmptyData() {
        // 准备测试数据
        TagDrillDownRequest request = new TagDrillDownRequest();
        request.setTagType(TagTypeEnum.DENGFENG_TRAINING.getCode());
        request.setPage(1);
        request.setLimit(20);

        // Mock返回空列表
        when(policeTagInfoMapper.getTagInfoForDrillDown(any())).thenReturn(new ArrayList<>());

        // 执行测试
        Page<PoliceBasicInfo> result = policeTagInfoService.getPoliceListByTagType(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotal());
        assertTrue(result.getRecords().isEmpty());
    }

    @Test
    void testGetPoliceListByTagType_WithData() {
        // 准备测试数据
        TagDrillDownRequest request = new TagDrillDownRequest();
        request.setTagType(TagTypeEnum.DENGFENG_TRAINING.getCode());
        request.setPage(1);
        request.setLimit(20);

        // 创建模拟标签数据
        PoliceTagInfo tagInfo = new PoliceTagInfo();
        tagInfo.setIdCards(Arrays.asList("110101199001011234", "110101199001011235"));
        tagInfo.setTagName(Arrays.asList("基础训练"));

        List<PoliceTagInfo> mockTagData = Arrays.asList(tagInfo);

        // 创建模拟人员数据
        PoliceBasicInfo person1 = new PoliceBasicInfo();
        person1.setIdCard("110101199001011234");
        person1.setName("张三");

        PoliceBasicInfo person2 = new PoliceBasicInfo();
        person2.setIdCard("110101199001011235");
        person2.setName("李四");

        Page<PoliceBasicInfo> mockPersonPage = new Page<>(1, 20);
        mockPersonPage.setRecords(Arrays.asList(person1, person2));
        mockPersonPage.setTotal(2);

        // Mock返回数据
        when(policeTagInfoMapper.getTagInfoForDrillDown(any())).thenReturn(mockTagData);
        when(policeTagInfoMapper.getPoliceByIdCards(any(), anyList(), anyString())).thenReturn(mockPersonPage);

        // 执行测试
        Page<PoliceBasicInfo> result = policeTagInfoService.getPoliceListByTagType(request);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.getTotal());
        assertEquals(2, result.getRecords().size());
        assertEquals("张三", result.getRecords().get(0).getName());
        assertEquals("李四", result.getRecords().get(1).getName());
    }
}
