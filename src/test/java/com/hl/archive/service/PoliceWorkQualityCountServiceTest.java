package com.hl.archive.service;

import com.hl.archive.domain.dto.PoliceWorkQualityCountReturnDTO;
import com.hl.archive.domain.dto.WorkQualityCountQueryDTO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.beans.factory.annotation.Autowired;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * 民警工作质态统计服务性能测试
 */
@SpringBootTest
@ActiveProfiles("test")
@Slf4j
public class PoliceWorkQualityCountServiceTest {

    @Autowired
    private PoliceWorkQualityCountService policeWorkQualityCountService;

    /**
     * 测试并发性能
     */
    @Test
    public void testConcurrentPerformance() {
        WorkQualityCountQueryDTO queryDTO = new WorkQualityCountQueryDTO();
        queryDTO.setPoliceNumber("123456");

        int threadCount = 10;
        int requestsPerThread = 5;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        long startTime = System.currentTimeMillis();

        CompletableFuture<Void>[] futures = new CompletableFuture[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            futures[i] = CompletableFuture.runAsync(() -> {
                for (int j = 0; j < requestsPerThread; j++) {
                    try {
                        // 测试各个方法的并发性能
                        Long zbCount = policeWorkQualityCountService.countZBAJ(queryDTO);
                        Long xbCount = policeWorkQualityCountService.countXBAJ(queryDTO);
                        PoliceWorkQualityCountReturnDTO measures = policeWorkQualityCountService.countAJCS(queryDTO);
                        Long caseExamineCount = policeWorkQualityCountService.caseExamineCount(queryDTO);
                        Long policeExamineCount = policeWorkQualityCountService.policeExamineCount(queryDTO);
                        Long casePlaceExamineCount = policeWorkQualityCountService.casePlaceExamineCounr(queryDTO);
                        PoliceWorkQualityCountReturnDTO timeoutCount = policeWorkQualityCountService.timeoutCount(queryDTO);
                        
                        log.info("Thread: {}, Request: {}, ZB: {}, XB: {}, CaseExamine: {}, PoliceExamine: {}, CasePlaceExamine: {}", 
                                Thread.currentThread().getName(), j, zbCount, xbCount, caseExamineCount, policeExamineCount, casePlaceExamineCount);
                    } catch (Exception e) {
                        log.error("并发测试异常", e);
                    }
                }
            }, executor);
        }

        CompletableFuture.allOf(futures).join();

        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;

        log.info("并发测试完成 - 线程数: {}, 每线程请求数: {}, 总请求数: {}, 总耗时: {}ms, 平均耗时: {}ms", 
                threadCount, requestsPerThread, threadCount * requestsPerThread, totalTime, totalTime / (threadCount * requestsPerThread));

        executor.shutdown();
        try {
            if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            executor.shutdownNow();
        }
    }

    /**
     * 测试单个方法性能
     */
    @Test
    public void testSingleMethodPerformance() {
        WorkQualityCountQueryDTO queryDTO = new WorkQualityCountQueryDTO();
        queryDTO.setPoliceNumber("123456");

        // 预热
        for (int i = 0; i < 10; i++) {
            policeWorkQualityCountService.countAJCS(queryDTO);
        }

        // 测试 countAJCS 方法性能
        long startTime = System.currentTimeMillis();
        int iterations = 100;
        
        for (int i = 0; i < iterations; i++) {
            PoliceWorkQualityCountReturnDTO result = policeWorkQualityCountService.countAJCS(queryDTO);
            // 验证结果不为空
            assert result != null;
            assert result.getXsCaseStat() != null;
            assert result.getZaCaseStat() != null;
        }
        
        long endTime = System.currentTimeMillis();
        long totalTime = endTime - startTime;
        
        log.info("countAJCS方法性能测试 - 迭代次数: {}, 总耗时: {}ms, 平均耗时: {}ms", 
                iterations, totalTime, (double)totalTime / iterations);
    }

    /**
     * 测试线程安全性
     */
    @Test
    public void testThreadSafety() {
        WorkQualityCountQueryDTO queryDTO = new WorkQualityCountQueryDTO();
        queryDTO.setPoliceNumber("123456");

        int threadCount = 20;
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        
        CompletableFuture<PoliceWorkQualityCountReturnDTO>[] futures = new CompletableFuture[threadCount];
        
        for (int i = 0; i < threadCount; i++) {
            futures[i] = CompletableFuture.supplyAsync(() -> {
                return policeWorkQualityCountService.countAJCS(queryDTO);
            }, executor);
        }

        CompletableFuture<PoliceWorkQualityCountReturnDTO[]> allResults = 
            CompletableFuture.allOf(futures)
                .thenApply(v -> {
                    PoliceWorkQualityCountReturnDTO[] results = new PoliceWorkQualityCountReturnDTO[threadCount];
                    for (int i = 0; i < threadCount; i++) {
                        results[i] = futures[i].join();
                    }
                    return results;
                });

        PoliceWorkQualityCountReturnDTO[] results = allResults.join();
        
        // 验证所有结果一致性（在相同查询条件下应该返回相同结果）
        PoliceWorkQualityCountReturnDTO firstResult = results[0];
        for (int i = 1; i < results.length; i++) {
            PoliceWorkQualityCountReturnDTO currentResult = results[i];
            
            // 验证刑事案件统计一致性
            assert firstResult.getXsCaseStat().getXschCount().equals(currentResult.getXsCaseStat().getXschCount());
            assert firstResult.getXsCaseStat().getJsjzCount().equals(currentResult.getXsCaseStat().getJsjzCount());
            assert firstResult.getXsCaseStat().getXsjlCount().equals(currentResult.getXsCaseStat().getXsjlCount());
            
            // 验证治安案件统计一致性
            assert firstResult.getZaCaseStat().getXzjlCount().equals(currentResult.getZaCaseStat().getXzjlCount());
            assert firstResult.getZaCaseStat().getChCount().equals(currentResult.getZaCaseStat().getChCount());
            assert firstResult.getZaCaseStat().getJgCount().equals(currentResult.getZaCaseStat().getJgCount());
        }
        
        log.info("线程安全性测试通过 - 所有{}个并发请求返回一致结果", threadCount);
        
        executor.shutdown();
    }
}
