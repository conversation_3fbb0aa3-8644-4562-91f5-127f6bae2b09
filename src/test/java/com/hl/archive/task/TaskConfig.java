package com.hl.archive.task;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;

import java.util.ArrayList;
import java.util.List;

public class TaskConfig {
    public static void main(String[] args) {
        List<String> formId = new ArrayList<>();

        List<String> configUuids = Lists.newArrayList("CNE2TZD2GTE",
                "CG9Z9HJ3FJW",
                "C30NIBEJEGI",
                "CKSWWQQWW7H",
                "C0QCUGONMLP",
                "CC42ITDEHRQ",
                "C09ZZEL29S8");
        configUuids.forEach(configUuid -> {
            String formId1 = getFormId(configUuid);
            String[] split = formId1.split(",");
            for (String s : split) {
                if (!formId.contains(s)) {
                    formId.add(s);
                }
            }
        });

        System.out.println(formId);

    }

    public static String getFormId(String configUuid) {

        JSONObject param = new JSONObject();
        param.put("config_uuid", configUuid);

        HttpResponse execute = HttpUtil.createPost("http://192.168.10.98:17080/task/utils/task_config_form")
                .header("token", "f71046a49c954951a0f4de9ae9603dee")
                .body(param.toJSONString())
                .execute();
        JSONObject jsonObject = JSONObject.parseObject(execute.body());
        String string = jsonObject.getByPath("data.form").toString();
        return string;
    }

}
