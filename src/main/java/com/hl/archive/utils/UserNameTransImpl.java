package com.hl.archive.utils;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hl.translation.annotation.TranslationType;
import com.hl.translation.core.TranslationInterface;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@TranslationType(type = TransConstants.ID_CARDS_TO_USER_LIST)
@Configuration
@Slf4j
public class UserNameTransImpl implements TranslationInterface<List<JSONObject>> {

    @Override
    public List<JSONObject> translation(Object o, String s) {
        List<JSONObject> result = new ArrayList<>();
        // 如果是 List<String>
        try {

            JSONArray from = JSONArray.from(o);
            List<String> list = from.toList(String.class);
            for (String string : list) {
                Object idCard = SsoCacheUtil.getUserObjByIdCard(string);
                result.add(JSONObject.from(idCard));
            }
        } catch (Exception e) {
            log.error("转换用户名称失败: {}", s, e);
        }
        return result;
    }
}
