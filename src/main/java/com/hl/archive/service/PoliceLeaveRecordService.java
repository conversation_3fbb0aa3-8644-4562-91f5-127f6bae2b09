package com.hl.archive.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.JSONPath;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.archive.domain.entity.PoliceLeaveRecord;
import com.hl.archive.feign.TaskApi;
import com.hl.archive.listener.RefreshDutyStatusEvent;
import com.hl.archive.listener.RefreshDutyStatusWithDeleteEvent;
import com.hl.archive.mapper.PoliceLeaveRecordMapper;
import com.hl.common.domain.R;
import com.hl.dict.DictCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PoliceLeaveRecordService extends ServiceImpl<PoliceLeaveRecordMapper, PoliceLeaveRecord> {

    @Value("${spring.security.sso.projectToken}")
    private String token;


    @Resource
    private TaskApi taskApi;

    @Resource
    private PoliceBasicInfoService policeBasicInfoService;


    @Scheduled(cron = "0 10 0 * * *")
    public void refreshLeaveRecord() {
        log.info("接收刷新在岗状态事件");

        DateTime start = DateUtil.endOfDay(DateUtil.date());
        DateTime end = DateUtil.beginOfDay(DateUtil.date());

        List<JSONObject> idCards = this.baseMapper.queryLeaveRecord(start.toString(), end.toString());
        if (idCards.isEmpty()) {
            policeBasicInfoService.update(Wrappers.<PoliceBasicInfo>lambdaUpdate()
                    .set(PoliceBasicInfo::getDutyStatus, "在岗"));
        } else {
            Set<String> idSet = idCards.stream().map(a -> a.getString("id_card")).collect(Collectors.toSet());
            log.info("当天休假身份证:{}", idSet);
            policeBasicInfoService.update(Wrappers.<PoliceBasicInfo>lambdaUpdate()
                    .set(PoliceBasicInfo::getDutyStatus, "在岗")
                    .notIn(PoliceBasicInfo::getIdCard, idSet));
            for (JSONObject object : idCards) {
                String idCard = object.getString("id_card");
                String leaveType = object.getString("leave_type");
                policeBasicInfoService.update(Wrappers.<PoliceBasicInfo>lambdaUpdate()
                        .set(PoliceBasicInfo::getDutyStatus, leaveType)
                        .eq(PoliceBasicInfo::getIdCard, idCard));
            }
        }
    }


    @EventListener(RefreshDutyStatusEvent.class)
    public void taskListen(RefreshDutyStatusEvent event) {
        JSONObject contentData = event.getContentData();

        String configUuid = contentData.getByPath("data.config_uuid").toString();

        switch (configUuid) {
            case "CC42ITDEHRQ":
                praseQXJ(contentData);
                break;
            case "CG9Z9HJ3FJW":
                praseCC(contentData);
                break;
        }
        // 刷新在岗状态
        this.refreshLeaveRecord();
    }

    // 请休假
    public void praseQXJ(JSONObject contentData) {
        try {
            String passStatus = contentData.getByPath("data.content.pass").toString();
            if (!"1".equals(passStatus)) {
                return;
            }
            String customId = contentData.getByPath("data.custom_id").toString();
            if (!"ZGLDSP".equals(customId)
                    && !"FGJLD".equals(customId)
                    && !"JLDSP".equals(customId)) {
                return;
            }
            String taskId = contentData.getByPath("data.task_id").toString();
            JSONObject param = new JSONObject();
            param.put("task_id", taskId);
            R<?> oneTask = taskApi.getOneTask(token, param);
            JSONObject parsed = JSONObject.from(oneTask.getData());
            String time = parsed.getByPath("content.qjkssj").toString();
            String[] timeArr = time.split("_");
            String qjkssj = timeArr[0];
            String qjjssj = timeArr[1];
            String qjlb = parsed.getByPath("content.qjlb").toString();
            List<String> idCardList = JSONArray.from(parsed.getByPath("content.qjry")).toJavaList(String.class);
            for (String s : idCardList) {
                PoliceLeaveRecord leaveRecord = new PoliceLeaveRecord();
                leaveRecord.setStartDate(DateUtil.beginOfDay(DateUtil.parseDate(qjkssj)).toLocalDateTime());
                leaveRecord.setEndDate(DateUtil.endOfDay(DateUtil.parseDate(qjjssj)).toLocalDateTime());
                leaveRecord.setIdCard(s);
                leaveRecord.setLeaveType(DictCache.ID_MAP_TREE.getJSONObject(qjlb).getString("name"));
                leaveRecord.setTaskId(taskId);
                this.save(leaveRecord);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }


    // 出差
    private void praseCC(JSONObject contentData) {
        try {
            String passStatus = contentData.getByPath("data.content.pass").toString();
            if (!"1".equals(passStatus)) {
                return;
            }
            String customId = contentData.getByPath("data.custom_id").toString();
            if (!"ZGLDSP".equals(customId)
                    && !"FGJLD".equals(customId)
                    && !"JLDSP".equals(customId)) {
                return;
            }
            String taskId = contentData.getByPath("data.task_id").toString();
            JSONObject param = new JSONObject();
            param.put("task_id", taskId);
            R<?> oneTask = taskApi.getOneTask(token, param);
            JSONObject parsed = JSONObject.from(oneTask.getData());
            String ccsj = parsed.getByPath("content.ccsj").toString();
            String[] timeArr = ccsj.split("_");
            List<String> idCardList = JSONArray.from(parsed.getByPath("content.ccyy")).toJavaList(String.class);
            for (String s : idCardList) {
                PoliceLeaveRecord leaveRecord = new PoliceLeaveRecord();
                leaveRecord.setStartDate(DateUtil.beginOfDay(DateUtil.parseDate(timeArr[0])).toLocalDateTime());
                leaveRecord.setEndDate(DateUtil.endOfDay(DateUtil.parseDate(timeArr[1])).toLocalDateTime());
                leaveRecord.setIdCard(s);
                leaveRecord.setLeaveType("出差");
                leaveRecord.setTaskId(taskId);
                this.save(leaveRecord);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }


    @EventListener(RefreshDutyStatusWithDeleteEvent.class)
    public void handleDeleteEvent(RefreshDutyStatusWithDeleteEvent event) {
        try {
            JSONObject contentData = event.getContentData();
            String taskId = contentData.getByPath("data.task_id").toString();
            this.remove(Wrappers.<PoliceLeaveRecord>lambdaQuery()
                    .eq(PoliceLeaveRecord::getTaskId, taskId));
            this.refreshLeaveRecord();
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }


}
