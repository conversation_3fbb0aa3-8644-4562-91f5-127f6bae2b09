package com.hl.archive.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.dynamic.datasource.annotation.Slave;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.entity.PoliceOverseasTravel;
import com.hl.archive.feign.TaskApi;
import com.hl.archive.listener.PoliceOverseasTravelApproveEvent;
import com.hl.archive.listener.PoliceOverseasTravelConfig;
import com.hl.archive.listener.PoliceOverseasTravelDeleteEvent;
import com.hl.archive.mapper.PoliceOverseasTravelMapper;
import com.hl.dict.DictCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;


@Service
@RequiredArgsConstructor
@Slf4j
public class PoliceOverseasTravelService extends ServiceImpl<PoliceOverseasTravelMapper, PoliceOverseasTravel> {

    private final PoliceOverseasTravelConfig policeOverseasTravelConfig;

    private final TaskApi taskApi;

    @Value("${spring.security.sso.projectToken}")
    private String token;

    @EventListener(PoliceOverseasTravelApproveEvent.class)
    public void handleApproveEvent(PoliceOverseasTravelApproveEvent event) {
        try {
            JSONObject taskData = event.getTaskData();
            String passStatus = taskData.getByPath("data.content.pass").toString();
            if (!"1".equals(passStatus)) {
                return;
            }
            String customId = taskData.getByPath("data.custom_id").toString();

            if (!"JDWSP".equals(customId) && !"ZWSP".equals(customId)){
                return;
            }
            String taskId = taskData.getByPath("data.task_id").toString();
            JSONObject param = new JSONObject();
            param.put("task_id", taskId);
            JSONObject parsed = JSONObject.from(taskApi.getOneTask(token, param).getData());
            String idCard = parsed.getByPath("all_content.id_card").toString();
            JSONObject content = parsed.getJSONObject("content");
            PoliceOverseasTravel overseasTravel = new PoliceOverseasTravel();
            overseasTravel.setIdCard(idCard);
            overseasTravel.setTaskId(taskId);

            List<String> destinationCountryList = content.getList(policeOverseasTravelConfig.getDestinationCountry(), String.class);
            List<String> destinationCountryName = new ArrayList<>();
            for (String destinationCountry : destinationCountryList) {
                if (DictCache.ID_MAP_TREE.containsKey(destinationCountry)) {
                    String dictName = DictCache.ID_MAP_TREE.getJSONObject(destinationCountry).getString("name");
                    destinationCountryName.add(dictName);
                }
            }
            overseasTravel.setDestinationCountry(String.join(",", destinationCountryName));
            overseasTravel.setTravelReason(content.getString(policeOverseasTravelConfig.getTravelReason()));
            String dataStr = content.getString(policeOverseasTravelConfig.getDate());
            if (dataStr.contains("_")) {
                String[] dataArr = dataStr.split("_");
                overseasTravel.setStartDate(DateUtil.parse(dataArr[0]).toLocalDateTime());
                overseasTravel.setEndDate(DateUtil.parse(dataArr[1]).toLocalDateTime());
            }
            overseasTravel.setDataType(1);
            this.save(overseasTravel);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    @EventListener(PoliceOverseasTravelDeleteEvent.class)
    public void handleDeleteEvent(PoliceOverseasTravelDeleteEvent event) {
        try {
            JSONObject taskData = event.getTaskData();
            String taskId = taskData.getByPath("data.task_id").toString();
            log.info("任务:{} 删除出国出境审批", taskId);
            this.remove(Wrappers.<PoliceOverseasTravel>lambdaQuery()
                    .eq(PoliceOverseasTravel::getTaskId, taskId));

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }
}
