package com.hl.archive.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hl.archive.domain.entity.PoliceAbilityTag;
import com.hl.archive.feign.TaskApi;
import com.hl.archive.listener.PoliceAbilityTagConfig;
import com.hl.archive.listener.PoliceAbilityTagDeleteEvent;
import com.hl.archive.listener.PoliceAbilityTagEvent;
import com.hl.archive.mapper.PoliceAbilityTagMapper;
import com.hl.common.domain.R;
import com.hl.dict.DictCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 民警职业能力标签服务接口
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class PoliceAbilityTagService extends ServiceImpl<PoliceAbilityTagMapper, PoliceAbilityTag> {

    @Value("${spring.security.sso.projectToken}")
    private String token;

    @Resource
    private TaskApi taskApi;

    private final PoliceAbilityTagConfig policeAbilityTagConfig;

    @EventListener(PoliceAbilityTagEvent.class)
    public void parseTaskAbilityTag(PoliceAbilityTagEvent event) {
        try {
            JSONObject taskData = event.getTaskData();
            log.info("职业能力标签:{}",taskData);
            String opt = taskData.getByPath("opt").toString();
            if (!"audit".equals(opt)){
                return;
            }
            String passStatus= taskData.getByPath("data.content.pass").toString();
            if (!"1".equals(passStatus)){
                return;
            }
            String taskId = taskData.getByPath("data.task_id").toString();
            JSONObject param = new JSONObject();
            param.put("task_id", taskId);
            R<?> oneTask = taskApi.getOneTask(token, param);
            JSONObject parsed = JSONObject.from(oneTask.getData());
            JSONObject content = parsed.getJSONObject("content");
            List<String> abilityTagCode = content.getList(policeAbilityTagConfig.getAbilityTagCode(), String.class);
            List<String> abilityTagName = new ArrayList<>();
            for (String tagCode : abilityTagCode) {
                if (DictCache.ID_MAP_TREE.containsKey(tagCode)) {
                    String dictName = DictCache.ID_MAP_TREE.getJSONObject(tagCode).getString("name");
                    abilityTagName.add(dictName);
                }
            }
            List<String> idCard = content.getList(policeAbilityTagConfig.getIdCard(), String.class);
            String description = content.getString(policeAbilityTagConfig.getDescription());
            String obtainTime = content.getString(policeAbilityTagConfig.getObtainTime());
            PoliceAbilityTag policeAbilityTag = new PoliceAbilityTag();
            policeAbilityTag.setAbilityTagCode(abilityTagCode);
            policeAbilityTag.setIdCard(idCard);
            policeAbilityTag.setDescription(description);
            policeAbilityTag.setObtainTime(DateUtil.parse(obtainTime).toLocalDateTime().toLocalDate());
            policeAbilityTag.setAbilityTagName(abilityTagName);
            policeAbilityTag.setTaskId(taskId);
            log.info("任务:{} 审批通过,新增职业能力标签:{}",taskId,policeAbilityTag);
            this.save(policeAbilityTag);
        } catch (Exception e) {
            log.error(e.getMessage(),e);
        }
    }



    @EventListener(PoliceAbilityTagDeleteEvent.class)
    public void handleDeleteEvent(PoliceAbilityTagDeleteEvent event) {
        String taskId = null;
        try {
            JSONObject taskData = event.getTaskData();
            taskId = taskData.getByPath("data.task_id").toString();
            log.info("任务:{} 删除职业能力标签", taskId);
            this.remove(Wrappers.<PoliceAbilityTag>lambdaQuery()
                    .eq(PoliceAbilityTag::getTaskId, taskId));
        } catch (Exception e) {
            log.info("任务:{} 删除职业能力标签失败", taskId);
            log.error(e.getMessage(), e);
        }
    }
}
