package com.hl.archive.controller;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.hl.archive.domain.entity.PoliceFamilyMembers;
import com.hl.archive.domain.request.PoliceBaseQueryRequest;
import com.hl.archive.feign.TaskApi;
import com.hl.archive.mapper.TaskInfoMapper;
import com.hl.archive.service.PoliceFamilyMembersService;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import com.hl.security.config.sso.api.SsoCacheApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/extendTask")
@RequiredArgsConstructor
@Api(tags = "任务接口封装")
@Slf4j
public class TaskFeignController {

    @Resource
    private TaskApi taskApi;

    private final TaskInfoMapper taskInfoMapper;

    private final PoliceFamilyMembersService policeFamilyMembersService;

    private final SsoCacheApi ssoCacheApi;

    @PostMapping("/getBusinessTripTask")
    @ApiOperation("出差查询")
    public R<?> businessTrip(@RequestBody JSONObject param) {
        String taskId = param.getString("task_id");
        String token = UserUtils.getUser().getToken();
        param.put("task_id", taskId);
        R<?> oneTask = taskApi.getOneTask(token, param);
        JSONObject parsed = JSONObject.from(oneTask.getData());
        Object byPath = parsed.getByPath("content.ccdd");
        if (byPath == null){
            return R.ok();
        }
//        String ccdd = byPath.toString();

        String[] split = parsed.getByPath("content.ccsj").toString().split("_");
        JSONObject queryParam = new JSONObject();
        queryParam.put("ccdd",byPath);
        queryParam.put("startDate",split[0]);
        queryParam.put("endDate",split[1]);
        List<JSONObject> jsonObjects = taskInfoMapper.queryCcTask(queryParam);

        Set<String> taskIds = jsonObjects.stream().map(jsonObject -> jsonObject.getString("task_id")).collect(Collectors.toSet());
        taskIds.remove(taskId);
        if (taskIds.isEmpty()){
            return R.ok();
        }

        JSONObject requestParam = new JSONObject();
        requestParam.put("task_ids",taskIds);
        requestParam.put("config_uuid","CG9Z9HJ3FJW");
        R<JSONObject> taskList = taskApi.getTaskList(token, requestParam);
//        requestParam.put("CG9Z9HJ3FJW#ccdd", ccdd);
//
//        requestParam.put("CG9Z9HJ3FJW#ccsj",DateUtil.now().toString()+"_");
//        requestParam.put("status",11);
//        R<JSONObject> taskList = taskApi.getTaskList(token, requestParam);
        Object data = taskList.getData();
        JSONObject from = JSONObject.from(data);
        if (from.containsKey("list") && !from.getJSONArray("list").isEmpty()) {
            JSONArray list = from.getJSONArray("list");
            for (int i = 0; i < list.size(); i++) {
                JSONObject jsonObject = list.getJSONObject(i);
                String ccsjStr = jsonObject.getString("ccsj_str");
                if (StrUtil.isNotBlank(ccsjStr)){
                    ccsjStr = ccsjStr.replace("_", "至");
                    jsonObject.put("ccsj_str", ccsjStr);
                }
                JSONArray ccddStr = jsonObject.getJSONArray("ccdd_str");
                if (ccddStr != null && !ccddStr.isEmpty()) {
                    String name = ccddStr.stream().map(a -> JSONObject.from(a).getString("name")).collect(Collectors.joining(","));
                    jsonObject.put("ccdd_str", name);
                }

                JSONArray ccsyStr = jsonObject.getJSONArray("ccsy_str");
                if (ccsyStr != null && !ccsyStr.isEmpty()) {
                    String name = ccsyStr.stream().map(a -> JSONObject.from(a).getString("name")).collect(Collectors.joining(","));
                    jsonObject.put("ccsy_str", name);
                }
            }
            from.put("list", list);
        }
        taskList.setData(from);
        return taskList;
    }


    @GetMapping("/getSpouseByIdCard")
    @ApiOperation("根据身份证号获取配偶信息")
    public R<PoliceFamilyMembers> getSpouseByIdCard(@RequestParam String idCard) {
        PoliceFamilyMembers one = policeFamilyMembersService.getOne(Wrappers.<PoliceFamilyMembers>lambdaQuery()
                .eq(PoliceFamilyMembers::getIdCard, idCard)
                .in(PoliceFamilyMembers::getRelationship, "妻子", "丈夫"));
        return R.ok(one);
    }




    @PostMapping("/queryDrinkReportTask")
    @ApiOperation("饮酒报告查询")
    public R<?> queryDrinkReportTask(@RequestBody PoliceBaseQueryRequest request) {
        JSONObject loginParam = new JSONObject();
        loginParam.put("id_card", request.getIdCard());
        loginParam.put("login_type", "3");
        R<JSONObject> login = ssoCacheApi.login(loginParam);
        String token = login.getData().getString("token");
        JSONObject param = new JSONObject();
        param.put("config_uuid", "C30NIBEJEGI");

        param.put("C30NIBEJEGI#D534aO62z4K_gfWJ_pass", Lists.newArrayList("1"));
        param.put("list_type", 31);
        param.put("type", 31);
        param.put("page",request.getPage());
        param.put("limit",request.getLimit());
        R<JSONObject> taskList = taskApi.getTaskList(token, param);
        return taskList;
    }
}
