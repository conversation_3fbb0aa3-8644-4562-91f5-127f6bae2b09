package com.hl.archive.controller;

import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceTagInfoXjdfAddDTO;
import com.hl.archive.domain.dto.PoliceTagInfoXjdfQueryDTO;
import com.hl.archive.domain.dto.PoliceTagInfoXjdfReturnDTO;

import com.hl.archive.service.PoliceTagInfoXjdfService;
import com.hl.common.domain.R;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/tagInfoDf")
@Api(tags = "先进典范")
@RequiredArgsConstructor
public class PoliceTagInfoXjdfController {


    private final PoliceTagInfoXjdfService policeTagInfoXjdfService;


    @PostMapping("/addTag")
    @ApiOperation("添加数据")
    public R<?> addTag(@RequestBody PoliceTagInfoXjdfAddDTO dto) {
        boolean result = policeTagInfoXjdfService.addTagData(dto);
        return R.ok(result);
    }

    @PostMapping("/listTag")
    @ApiOperation("列出聚合数据")
    public R<?> listTag(@RequestBody PoliceTagInfoXjdfQueryDTO dto) {
        Page<PoliceTagInfoXjdfReturnDTO> page = policeTagInfoXjdfService.listTag(dto);
        return R.ok(page.getRecords(),(int) page.getTotal());
    }

    @PostMapping("/batchDelete")
    @ApiOperation("批量删除")
    public R<?> batchDelete(@RequestBody JSONObject param) {
        boolean b = policeTagInfoXjdfService.removeBatchByIds(param.getList("ids", String.class));
        return R.ok(b);
    }



}
