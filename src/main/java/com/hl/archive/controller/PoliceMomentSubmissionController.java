package com.hl.archive.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.dto.PoliceMomentSubmissionAddDTO;
import com.hl.archive.domain.dto.PoliceMomentSubmissionQueryDTO;
import com.hl.archive.domain.entity.PoliceMomentSubmission;
import com.hl.archive.domain.entity.PoliceMomentSubmissionVideo;
import com.hl.archive.domain.request.PoliceBaseQueryRequest;
import com.hl.archive.service.PoliceMomentSubmissionService;
import com.hl.archive.service.PoliceMomentSubmissionVideoService;
import com.hl.archive.utils.SsoCacheUtil;
import com.hl.common.domain.R;
import com.hl.security.UserUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/momentSubmission")
@RequiredArgsConstructor
@Api(tags = "警彩瞬间")
public class PoliceMomentSubmissionController {

    private final PoliceMomentSubmissionService policeMomentSubmissionService;

    private final PoliceMomentSubmissionVideoService policeMomentSubmissionVideoService;


    @PostMapping("/addMomentSubmission")
    @ApiOperation("添加警彩瞬间")
    public R<Boolean> addMomentSubmission(@RequestBody PoliceMomentSubmissionAddDTO request) {
        PoliceMomentSubmission policeMomentSubmission = new PoliceMomentSubmission();
        policeMomentSubmission.setSubmitter(UserUtils.getUser().getName());
        List<String> officerName = request.getOfficerName();

        String name = officerName.stream().map(s -> {
            JSONObject userInfo = JSONObject.from(SsoCacheUtil.getUserObjByIdCard(s));
            return userInfo.getString("name");
        }).collect(Collectors.joining(","));
        policeMomentSubmission.setOfficerName(name);

        Set<String> orgIdSet = officerName.stream().map(SsoCacheUtil::getUserOrgIdByIdCard).collect(Collectors.toSet());
        String orgName = orgIdSet.stream().map(SsoCacheUtil::getOrganizationName).collect(Collectors.joining(","));
        policeMomentSubmission.setSubmitUnit(orgName);

        policeMomentSubmission.setDataType(request.getDataType());
        policeMomentSubmission.setMaterialType(request.getMaterialType());
        policeMomentSubmission.setSubmissionType(request.getSubmissionType());
        policeMomentSubmission.setSubmissionTime(request.getSubmissionTime());

        policeMomentSubmission.setCreateBy(UserUtils.getUser().getIdCard());
        policeMomentSubmission.setCreatedAt(LocalDateTime.now());
        policeMomentSubmission.setTitle(request.getTitle());
        boolean save = policeMomentSubmissionService.save(policeMomentSubmission);
        policeMomentSubmission.setZjbh(policeMomentSubmission.getId().toString());
        policeMomentSubmissionService.updateById(policeMomentSubmission);
        Long id = policeMomentSubmission.getId();
        List<PoliceMomentSubmissionVideo> fileList = request.getFileList();
        for (PoliceMomentSubmissionVideo submissionVideo : fileList) {
            submissionVideo.setId(null);
            submissionVideo.setSbZjbh(id.toString());
            submissionVideo.setDataType(request.getDataType());
        }
        boolean saveBatch = policeMomentSubmissionVideoService.saveBatch(fileList);
        return R.ok(save && saveBatch);
    }


    @PostMapping("/queryMomentSubmission")
    @ApiOperation("查询警彩瞬间")
    public R<List<PoliceMomentSubmission>> queryMomentSubmission(@RequestBody PoliceMomentSubmissionQueryDTO request) {

        String officerName = null;
        String organization = null;
        if (StrUtil.isNotBlank(request.getIdCard())) {
            String idCard = request.getIdCard();
            JSONObject userInfo = JSONObject.from(SsoCacheUtil.getUserObjByIdCard(idCard));
            officerName = userInfo.getByPath("name").toString();
            organization = userInfo.getByPath("organization[0].organization_name").toString();
        } else if (StrUtil.isNotBlank(request.getOrganizationId())) {
            organization = SsoCacheUtil.getOrganizationName(request.getOrganizationId());
        }
        if (organization != null) {
            organization = organization.replaceAll("派出所", "")
                    .replaceAll("武进分局", "");
        }

        Page<PoliceMomentSubmission> page = policeMomentSubmissionService.page(Page.of(request.getPage(), request.getLimit()), Wrappers.<PoliceMomentSubmission>lambdaQuery()
                .like(StrUtil.isNotBlank(officerName), PoliceMomentSubmission::getOfficerName, officerName)
                .like(StrUtil.isNotBlank(officerName), PoliceMomentSubmission::getSubmitUnit, organization));
        if (page.getRecords().isEmpty()) {
            return R.ok(page.getRecords(), (int) page.getTotal());
        }

        List<String> zjList = page.getRecords().stream().map(PoliceMomentSubmission::getZjbh).collect(Collectors.toList());
        List<PoliceMomentSubmissionVideo> list = policeMomentSubmissionVideoService.list(Wrappers.<PoliceMomentSubmissionVideo>lambdaQuery()
                .in(PoliceMomentSubmissionVideo::getSbZjbh, zjList));
        Map<String, List<PoliceMomentSubmissionVideo>> collect = list.stream().collect(Collectors.groupingBy(PoliceMomentSubmissionVideo::getSbZjbh));
        for (PoliceMomentSubmission record : page.getRecords()) {
            record.setFileList(collect.get(record.getZjbh()));
        }

        return R.ok(page.getRecords(), (int) page.getTotal());
    }


    @PostMapping("/deleteMomentSubmission")
    @ApiOperation("删除警彩瞬间")
    public R<Boolean> deleteMomentSubmission(@RequestBody JSONObject param) {
        List<String> ids = param.getJSONArray("ids").toJavaList(String.class);
        return R.ok(policeMomentSubmissionService.removeBatchByIds(ids));
    }

}
