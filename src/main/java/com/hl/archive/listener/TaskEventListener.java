package com.hl.archive.listener;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.DbUtil;
import cn.hutool.db.Entity;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.hl.archive.feign.TaskApi;
import com.hl.archive.service.PoliceLeaveRecordService;
import com.hl.archive.utils.DictUtils;
import com.hl.common.domain.R;
import com.hl.security.config.sso.api.SsoCacheApi;
import com.hl.security.config.sso.cache.SsoCache;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Component
@Slf4j
@RequiredArgsConstructor
public class TaskEventListener {

    @Value("${spring.security.sso.projectToken}")
    private String token;


    @Resource
    private PoliceLeaveRecordService policeLeaveRecordService;

    @Resource
    private TaskApi taskApi;

    @Resource
    private SsoCacheApi ssoCacheApi;

    private final ApplicationEventPublisher eventPublisher;


    @Resource
    @Qualifier("datasource2DataSource")
    private DataSource jqDataSource;


    @RabbitListener(queues = "wj_task_archive_s")
    public R<?> rabbitListener(Message message, Channel channel) throws IOException {
        try {
            String msgBody = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("接收到消息: {}", msgBody);
            ThreadUtil.execAsync(() -> {
                handleData(msgBody);
            });
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            // 处理失败，是否需要重试或丢弃可根据业务设定
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
        }
        return R.ok();
    }


    private void handleData(String msg) {
        try {
            JSONObject data = JSONObject.parseObject(msg);
            JSONObject contentData = JSONObject.parseObject(data.getString("content_data"));
            String opt = contentData.getString("opt");

            String configUuid = (String) contentData.getByPath("data.config_uuid");
            if (StrUtil.isBlank(configUuid)) {
                return;
            }

            switch (configUuid) {
//                case "C1FI31VEE7X":
                case "CC42ITDEHRQ":
                    // 请休假
                    if ("audit".equals(opt)) {
                        eventPublisher.publishEvent(new RefreshDutyStatusEvent(contentData));
                        addLCBB(contentData, "CC42ITDEHRQ");
                    }
                    if ("delete".equals(opt)){
                        // 同步删除
                        eventPublisher.publishEvent(new RefreshDutyStatusWithDeleteEvent(contentData));
                    }
                    break;
                case "CG9Z9HJ3FJW":
                    // 出差
                    if ("audit".equals(opt)) {
                        eventPublisher.publishEvent(new RefreshDutyStatusEvent(contentData));
                        addLCBB(contentData, "CG9Z9HJ3FJW");
                    }
                    if ("delete".equals(opt)){
                        eventPublisher.publishEvent(new RefreshDutyStatusWithDeleteEvent(contentData));
                    }

                    break;
                case "CNE2TZD2GTE":
                    // 职业能力
                    if ("audit".equals(opt)) {
                        eventPublisher.publishEvent(new PoliceAbilityTagEvent(contentData));
                    }
                    if ("delete".equals(opt)){
                        eventPublisher.publishEvent(new PoliceAbilityTagDeleteEvent(contentData));
                    }
                    break;
                case "CKSWWQQWW7H":
                    if ("audit".equals(opt)) {
                        eventPublisher.publishEvent(new PoliceOverseasTravelApproveEvent(contentData));
                    }
                    if ("delete".equals(opt)){
                        eventPublisher.publishEvent(new PoliceOverseasTravelDeleteEvent(contentData));
                    }
                    break;
                default:
                    break;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }


    }


    //添加离常报备
    private void addLCBB(JSONObject contentData, String type) {
        String passStatus = contentData.getByPath("data.content.pass").toString();
        if (!"1".equals(passStatus)) {
            return;
        }
        String customId = contentData.getByPath("data.custom_id").toString(); //判断是不是最后一个审批节点
        log.info("customId------> {}", customId);
        if (!customId.equals("ZGLDSP") && !customId.equals("FGJLD") && !customId.equals("JLDSP")) {
            return; // 不是最后一个审批节点
        }

        String taskId = contentData.getByPath("data.task_id").toString();
        JSONObject param = new JSONObject();
        param.put("task_id", taskId);
        R<?> oneTask = taskApi.getOneTask(token, param);
        JSONObject parsed = JSONObject.from(oneTask.getData());
        String idCard = parsed.getByPath("all_content.id_card").toString();

        String zwjb = parsed.getByPath("content.zwjb").toString();
        final Map<String, String> APPROVAL_MAPPING = new HashMap<>();
        {
            APPROVAL_MAPPING.put("ZGLDSP", "普通民警");
            APPROVAL_MAPPING.put("FGJLD", "股级干部");
            APPROVAL_MAPPING.put("JLDSP", "科级干部");
        }
        if (APPROVAL_MAPPING.getOrDefault(customId, "").equals(zwjb)) {
            JSONObject obj = new JSONObject();
            obj.put("config_uuid", "C09ZZEL29S8"); // 离常报备
            if ("CC42ITDEHRQ".equals(type)) { // 请休假
                if (parsed.getByPath("content.sflc").equals("否")) {
                    return;
                }
                obj.put("lcbbr", parsed.getByPath("content.qjry")); // 离常报备人 - 请假人员
                obj.put("wcsy", parsed.getByPath("content.qjsy")); // 离常事由 - 请假事由
                obj.put("lksj", parsed.getByPath("content.qjkssj")); // 离开时间 - 外出时间
                obj.put("wcdd", parsed.getByPath("content.qjwcdd")); // 外出地点 - 外出地点
                obj.put("lsdzryzw", parsed.getByPath("content.zwjb")); // 职务级别
                obj.put("lsdzry", parsed.getByPath("content.qjlsdzry")); // 临时代职人员
                obj.put("bz", ""); // 备注
                obj.put("fgld", parsed.getByPath("content.fgld")); // 分管领导
                obj.put("jld", parsed.getByPath("content.jld")); // 局领导
                obj.put("sign_url", parsed.getByPath("content.sign_url"));
            } else if ("CG9Z9HJ3FJW".equals(type)) { // 出差
                obj.put("lcbbr", parsed.getByPath("content.ccyy")); // 离常报备人 - 出差人员
                obj.put("lksj", parsed.getByPath("content.ccsj")); // 离开时间 - 出差时间
                obj.put("wcdd", parsed.getByPath("content.ccdd")); // 外出地点 - 出差地点
                obj.put("lsdzryzw", parsed.getByPath("content.zwjb")); // 职务级别
                obj.put("lsdzry", parsed.getByPath("content.lsdzry")); // 临时代职人员
                obj.put("bz", ""); // 备注
                obj.put("fgld", parsed.getByPath("content.fgld")); // 分管领导
                obj.put("jld", parsed.getByPath("content.jld")); // 局领导
                obj.put("sign_url", parsed.getByPath("content.sign_url"));

                // 处理出差时的字典类型
                Object dict = parsed.getByPath("content.ccsy");
                JSONArray dictArr = JSONArray.from(dict);
                JSONArray dictNameArr = new JSONArray();
                for (int i = 0; i < dictArr.size(); i++) {
                    String dictName = DictUtils.getDictValue("away_type", (String) dictArr.get(i)).getData();
                    dictNameArr.add(dictName);
                }
                obj.put("wcsy", dictNameArr); // 离常事由-出差事由字典转换
                log.info("字典处理前-{}", parsed.getByPath("content.ccsy"));
                log.info("字典处理后-{}", dictNameArr);
//                obj.put("wcsy", parsed.getByPath("content.ccsy")); // 离常事由-出差事由
//                obj.put("title", parsed.getByPath("content.ccsy"));
            }
            log.info("obj------> {}", obj);

            JSONObject loginObj = new JSONObject();
            loginObj.put("id_card", idCard);
            loginObj.put("login_type", "3");
            log.info("创建离常时-登录信息--->{}", loginObj);
            R<JSONObject> login = ssoCacheApi.login(loginObj);
            String taskTODOId = "";
            if (R.isError(login)) {
                log.error("登录失败");
            } else {
                String loginToken = login.getData().getString("token");
                R<JSONArray> r = taskApi.add(loginToken, obj);
                if (R.isSuccess(r)) {
                    log.info("添加了一个任务");
                    log.info("添加结果-->{}", r.getData());  //返回task_id （待审核的任务id）
                    taskTODOId = r.getData().getString(0);
                } else {
                    log.error("添加任务时失败");
                    return;
                }
            }

            // 开始审批
            final Map<String, List<String>> AUDIT_MAPPING = new HashMap<>();
            {
                AUDIT_MAPPING.put("普通民警", Arrays.asList("FGLDSP", "ZGLDSP"));
                AUDIT_MAPPING.put("股级干部", Arrays.asList("GJZGLDSP", "FGJLD"));
                AUDIT_MAPPING.put("科级干部", Arrays.asList("KJFGJLDSP", "JLDSP"));
            }
            boolean audit1 = audit(taskId, AUDIT_MAPPING.get(zwjb).get(0), taskTODOId);
            if (!audit1) {
                return;
            }
            boolean audit2 = audit(taskId, AUDIT_MAPPING.get(zwjb).get(1), taskTODOId);
        } else {
            log.error("没有对应的审批节点");
        }
    }

    /**
     * 审批
     *
     * @param taskId     用于查询审批人
     * @param customId   对应的节点
     * @param taskTODOId 待审批的任务 由添加离常任务返回
     */
    public boolean audit(String taskId, String customId, String taskTODOId) {
        try {
            log.info("f**************------------>");
            List<Entity> entity = DbUtil.use(jqDataSource).query(
                    "select * from task_exchange where topic = 'wj_task_archive_s' " +
                            "AND content_data->>'$.opt' = 'audit'  " +
                            "AND content_data->>'$.data.content.pass' =  1  " +
                            "AND content_data->>'$.data.task_id' =   '" + taskId + "'" +
                            "AND content_data->>'$.data.custom_id' =   '" + customId + "'" +
                            "order by content_time desc " +
                            "limit 1"
            );
            if (entity.isEmpty()) {
                log.error("task_exchange查询无结果");
                return false;
            }
            String entityStr = entity.get(0).toString();
            log.info("f**************------------>{}", entityStr);
            // 提取 id_card
            Pattern idCardPattern = Pattern.compile("\"id_card\"\\s*:\\s*\"([^\"]+)\"");
            Matcher idCardMatcher = idCardPattern.matcher(entityStr);
            String idCard = idCardMatcher.find() ? idCardMatcher.group(1) : null;
            // 提取 sign_url
            Pattern signUrlPattern = Pattern.compile("\"sign_url\"\\s*:\\s*\"([^\"]+)\"");
            Matcher signUrlMatcher = signUrlPattern.matcher(entityStr);
            String signUrl = signUrlMatcher.find() ? signUrlMatcher.group(1) : null;
            log.info("idCard------------>{}", idCard);
            log.info("signUrl------------>{}", signUrl);

            String token = getToken(idCard);
            if (StringUtils.isNotBlank(token)) {
                JSONObject content = new JSONObject();
                content.put("pass", 1);
                content.put("text", "系统自动审批");
                content.put("sign_url", signUrl);

                JSONObject obj = new JSONObject();
                obj.put("task_id", taskTODOId);
                obj.put("content", content);
                log.info("审批任务传参------> {}", obj);
                R<JSONArray> r = taskApi.audit(token, obj);
                if (R.isSuccess(r)) {
                    log.info("{}-审批成功", taskTODOId);
                    return true;
                } else {
                    log.error("{}-审批失败", taskTODOId);
                    return false;
                }

            }
        } catch (Exception e) {  // 捕获所有异常
            log.error("audit() 执行失败:", e);  // 打印完整异常堆栈
        }
        return false;
    }

    /**
     * 获取对应身份证的token
     *
     * @param idCard
     * @return
     */
    public String getToken(String idCard) {
        JSONObject loginObj = new JSONObject();
        loginObj.put("id_card", idCard);
        loginObj.put("login_type", "3");
        log.info("登录信息--->{}", loginObj);
        R<JSONObject> login = ssoCacheApi.login(loginObj);
        if (R.isError(login)) {
            log.error("登录失败");
            return null;
        }
        String loginToken = login.getData().getString("token");
        return loginToken;
    }

    public String getPhoneByName(String name) {
        JSONObject user = SsoCache.me.cacheData.getJSONObject("user_all");
        Set<String> keySet = user.keySet();
        for (String key : keySet) {
            JSONObject userObj = user.getJSONObject(key);
            if (userObj.getString("name").equals(name)) {
                return userObj.getString("phone");
            }
        }
        return null;
    }

}
