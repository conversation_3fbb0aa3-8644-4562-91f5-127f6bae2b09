package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 表彰奖励-单位荣誉
 */
@Data
@TableName(value = "police_archive.police_unit_award")
public class PoliceUnitAward {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;



    /**
     * 奖励名称
     */
    @TableField(value = "award_name")
    private String awardName;

    /**
     * 受奖时间
     */
    @TableField(value = "award_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime awardTime;

    /**
     * 奖励机关
     */
    @TableField(value = "award_organ")
    private String awardOrgan;

    /**
     * 表彰文件文号
     */
    @TableField(value = "document_number")
    private String documentNumber;

    /**
     * 获奖时主管姓名
     */
    @TableField(value = "supervisor_name")
    private String supervisorName;

    /**
     * 获奖时主管警号
     */
    @TableField(value = "supervisor_code")
    private String supervisorCode;

    /**
     * 所属单位
     */
    @TableField(value = "unit")
    private String unit;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    private LocalDateTime createdAt;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 修改人
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 是否删除（0 否，1 是）
     */
    @TableField(value = "is_deleted")
    private Boolean isDeleted;


    @TableField(value = "zjbh")
    private String zjbh;

}