package com.hl.archive.domain.entity;

import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.annotation.*;
import com.baomidou.mybatisplus.extension.handlers.Fastjson2TypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Date;
import lombok.Data;

/**
 * 表彰奖励表
 */
@ApiModel(description = "表彰奖励表")
@Data
@TableName(value = "police_honors",autoResultMap = true)
public class PoliceHonors {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @ApiModelProperty(value = "主键ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    @ApiModelProperty(value = "身份证号")
    private String idCard;

    /**
     * 荣誉名称
     */
    @TableField(value = "honor_name")
    @ApiModelProperty(value = "荣誉名称")
    private String honorName;

    /**
     * 获得时间
     */
    @TableField(value = "award_date")
    @ApiModelProperty(value = "获得时间")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate awardDate;

    /**
     * 批准机关名称
     */
    @TableField(value = "approve_authority")
    @ApiModelProperty(value = "批准机关名称")
    private String approveAuthority;

    /**
     * 逻辑删除(0:未删除,1:已删除)
     */
    @TableField(value = "is_deleted")
    @ApiModelProperty(value = "逻辑删除(0:未删除,1:已删除)")
    @TableLogic
    private Byte isDeleted;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at")
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 创建人身份证号
     */
    @TableField(value = "created_by")
    @ApiModelProperty(value = "创建人身份证号")
    private String createdBy;

    /**
     * 更新人身份证号
     */
    @TableField(value = "updated_by")
    @ApiModelProperty(value = "更新人身份证号")
    private String updatedBy;

    @TableField(value = "bh")
    private String bh;


    @TableField(value = "source_type")
    @ApiModelProperty(value = "数据来源类型")
    private Integer sourceType;

    @TableField(exist = false)
    @Translation(type = TransConstants.ID_CARD_TO_USER_OBJ, mapper = "idCard")
    private JSONObject userInfo;

    @TableField(value = "files",typeHandler = Fastjson2TypeHandler.class)
    private JSONArray files;

    @TableField(value = "organization_id")
    private String organizationId;
}