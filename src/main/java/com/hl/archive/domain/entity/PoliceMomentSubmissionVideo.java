package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.hl.archive.utils.TransConstants;
import com.hl.translation.annotation.Translation;
import lombok.Data;

/**
 * 警彩瞬间-素材上报
 */
@Data
@TableName(value = "police_archive.police_moment_submission_video")
public class PoliceMomentSubmissionVideo {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    private LocalDateTime createdAt;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 修改人
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 是否删除（0 否，1 是）
     */
    @TableField(value = "is_deleted")
    private Boolean isDeleted;

    /**
     * 主键编号
     */
    @TableField(value = "zjbh")
    private String zjbh;

    /**
     * 上报主键编号
     */
    @TableField(value = "sb_zjbh")
    private String sbZjbh;

    /**
     * 文件类型
     */
    @TableField(value = "file_type")
    private String fileType;

    /**
     * 文件名称
     */
    @TableField(value = "file_name")
    private String fileName;

    /**
     * 文件地址
     */
    @TableField(value = "file_url")
    @Translation(type = TransConstants.PROJECT_FILE_URL)
    private String fileUrl;

    private Integer dataType;
}