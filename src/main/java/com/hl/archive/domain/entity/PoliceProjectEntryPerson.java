package com.hl.archive.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import java.time.LocalDateTime;
import java.util.Date;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 星火计划-入项个人
 */
@Data
@TableName(value = "police_archive.police_project_entry_person")
public class PoliceProjectEntryPerson {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 身份证号
     */
    @TableField(value = "id_card")
    private String idCard;

    /**
     * 姓名
     */
    @TableField(value = "`name`")
    private String name;

    /**
     * 警号
     */
    @TableField(value = "police_number")
    private String policeNumber;

    /**
     * 所在单位
     */
    @TableField(value = "unit")
    private String unit;

    /**
     * 现最高荣誉级别
     */
    @TableField(value = "honor_level")
    private String honorLevel;

    /**
     * 现最高荣誉名称
     */
    @TableField(value = "honor_name")
    private String honorName;

    /**
     * 入项时间
     */
    @TableField(value = "entry_time")
    private LocalDateTime entryTime;

    /**
     * 培养联系人
     */
    @TableField(value = "contact_person")
    private String contactPerson;

    /**
     * 审核状态
     */
    @TableField(value = "audit_status")
    private String auditStatus;

    /**
     * 创建时间
     */
    @TableField(value = "created_at")
    private LocalDateTime createdAt;

    /**
     * 创建人
     */
    @TableField(value = "create_by")
    private String createBy;

    /**
     * 修改时间
     */
    @TableField(value = "updated_at")
    private LocalDateTime updatedAt;

    /**
     * 修改人
     */
    @TableField(value = "update_by")
    private String updateBy;

    /**
     * 是否删除（0 否，1 是）
     */
    @TableField(value = "is_deleted")
    private Boolean isDeleted;

    /**
     * 星火计划编号
     */
    @TableField(value = "zjbh")
    private String zjbh;

    @TableField(value = "organization_id")
    private String organizationId;
}