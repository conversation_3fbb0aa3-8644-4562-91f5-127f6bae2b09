package com.hl.archive.domain.dto;

import lombok.Data;

@Data
public class PoliceStatisticsDTO {
    private String department;

    private String departmentName;
    private Integer totalCount = 0;
    private Integer age20To30 = 0;
    private Integer age30To40 = 0;
    private Integer age40To50 = 0;
    private Integer age50Up = 0;
    private Integer eduZhuan = 0; // 大专
    private Integer eduBen = 0;   // 本科
    private Integer eduShuo = 0;  // 硕士
    private Integer eduBoshi = 0; // 博士
    private Double avgAge = 0.0;
    private Integer maleCount = 0;   // 男性人数
    private Integer femaleCount = 0; // 女性人数
    private Integer onDutyCount = 0; // 在岗人数 (dutyStatus = 0)
    private Integer offDutyCount = 0; // 不在岗人数 (dutyStatus != 0)
} 