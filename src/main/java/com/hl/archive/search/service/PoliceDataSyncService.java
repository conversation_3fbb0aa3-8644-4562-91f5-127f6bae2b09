package com.hl.archive.search.service;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.archive.search.document.PoliceBaseSearchDocument;
import com.hl.archive.search.mapper.PoliceBaseSearchDocumentMapper;
import com.hl.archive.service.PoliceBasicInfoService;
import com.hl.archive.utils.AnnotationUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class PoliceDataSyncService {

    private final PoliceBaseSearchDocumentMapper policeBaseSearchDocumentMapper;

    private final PoliceBasicInfoService policeBasicInfoService;

    public boolean syncPoliceBasicInfo() {
        List<PoliceBasicInfo> list = policeBasicInfoService.list();

        List<PoliceBaseSearchDocument> documents = new ArrayList<>();
        for (PoliceBasicInfo policeBasicInfo : list) {
            PoliceBaseSearchDocument document = new PoliceBaseSearchDocument();
            JSONObject data = JSON.parseObject(JSON.toJSONString(policeBasicInfo));
//            String content = data.entrySet().stream()
//                    .map(e -> e.getKey() + "：" + e.getValue())
//                    .collect(Collectors.joining("<br>"));

            String content = data.entrySet().stream()
                    .map(e -> AnnotationUtil.getApiModelPropertyValue(PoliceBasicInfo.class,e.getKey()) + "：" + e.getValue())
                    .collect(Collectors.joining("<br>"));
            document.setContent(content);
            document.setIdCard(policeBasicInfo.getIdCard());
            document.setDataType("basic_info");
            document.setDataTypeName("基本信息");
            document.setName(policeBasicInfo.getName());
            document.setRecordId(policeBasicInfo.getId());
            document.setImgUrl(policeBasicInfo.getImgUrl());
            document.setPoliceNumber(policeBasicInfo.getPoliceNumber());
            document.setDepartment(policeBasicInfo.getDepartment());
            document.setLeadershipLevel(policeBasicInfo.getLeadershipLevel());
            document.setPositionName(policeBasicInfo.getPositionName());
            document.setOrganizationId(policeBasicInfo.getOrganizationId());
            documents.add(document);
        }

        Integer i = policeBaseSearchDocumentMapper.insertBatch(documents);
        return  i > 0;
    }
}
