package com.hl.orasync.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;


/**
 * 基本信息
 */
@Data
@TableName(value = "ZTJC.V_WJ_RYJBXX")
public class VWjRyjbxx {
    @TableField(value = "ZP")
    private String zp;

    @TableField(value = "XM")
    private String xm;

    @TableField(value = "GMSFHM")
    private String gmsfhm;

    @TableField(value = "XB")
    private String xb;

    @TableField(value = "CSRQ")
    private String csrq;

    @TableField(value = "JG")
    private String jg;

    @TableField(value = "MZ")
    private String mz;

    @TableField(value = "JH")
    private String jh;

    @TableField(value = "DWMC")
    private String dwmc;

    @TableField(value = "BM")
    private String bm;

    @TableField(value = "LDZWCJ")
    private String ldzwcj;

    @TableField(value = "CJGAGZRQ")
    private String cjgagzrq;

    @TableField(value = "CJGZRQ")
    private String cjgzrq;

    @TableField(value = "SG")
    private String sg;

    @TableField(value = "TZ")
    private String tz;

    @TableField(value = "XX")
    private String xx;
}