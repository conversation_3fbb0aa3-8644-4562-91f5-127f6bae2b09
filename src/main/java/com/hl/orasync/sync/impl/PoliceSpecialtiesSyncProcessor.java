package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceSpecialties;
import com.hl.archive.service.PoliceSpecialtiesService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.domain.VWjRyjtcy;
import com.hl.orasync.domain.VWjRytc;
import com.hl.orasync.service.VWjRytcService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

@Component
@RequiredArgsConstructor
public class PoliceSpecialtiesSyncProcessor implements DataSyncProcessor<VWjRytc, PoliceSpecialties> {

    private final PoliceSpecialtiesService policeSpecialtiesService;


    private final VWjRytcService vwjRytcService;

    private final Converter converter;

    @Override
    public List<VWjRytc> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));

        Page<VWjRytc> page = vwjRytcService.page(Page.of(offset, limit), Wrappers.<VWjRytc>lambdaQuery()
                .orderByDesc(VWjRytc::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();

        return page.getRecords();
    }

    @Override
    public List<PoliceSpecialties> getTargetData(int offset, int limit) {
        Page<PoliceSpecialties> page = policeSpecialtiesService.page(Page.of(offset, limit),Wrappers.<PoliceSpecialties>lambdaQuery()
                .eq(PoliceSpecialties::getDataType,0));
        return page.getRecords();
    }

    @Override
    public PoliceSpecialties convert(VWjRytc source) {

        return converter.convert(source, PoliceSpecialties.class);
    }

    @Override
    public Function<PoliceSpecialties, String> getBusinessKeyGenerator() {
        return policeSpecialties ->
                policeSpecialties.getIdCard() + "_" + policeSpecialties.getSpecialtyName() + "_"
                        + policeSpecialties.getAwardDate()+"_"+policeSpecialties.getApproveAuthority();
    }

    @Override
    public void batchInsert(List<PoliceSpecialties> records) {
        policeSpecialtiesService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceSpecialties> records) {
        policeSpecialtiesService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeSpecialtiesService.removeBatchByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vwjRytcService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {

        return policeSpecialtiesService.count();
    }
}
