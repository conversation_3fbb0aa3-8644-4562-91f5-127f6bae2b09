package com.hl.orasync.sync.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hl.archive.domain.entity.PoliceBasicInfo;
import com.hl.archive.service.PoliceBasicInfoService;
import com.hl.common.config.datasource.DataSourceType;
import com.hl.common.config.datasource.DynamicDataSourceContextHolder;
import com.hl.orasync.convert.PoliceBasicInfoConvert;
import com.hl.orasync.domain.VWjRyjbxx;
import com.hl.orasync.service.VWjRyjbxxService;
import com.hl.orasync.sync.DataSyncProcessor;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;


@Component
@RequiredArgsConstructor
public class PoliceBasicInfoSyncProcessor implements DataSyncProcessor<VWjRyjbxx, PoliceBasicInfo> {


    private final PoliceBasicInfoService policeBasicInfoService;

    private final VWjRyjbxxService vwjRyjbxxService;


    @Override
    public List<VWjRyjbxx> getSourceData(int offset, int limit) {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        Page<VWjRyjbxx> page = vwjRyjbxxService.page(Page.of(offset, limit), Wrappers.<VWjRyjbxx>lambdaQuery()
                .orderByDesc(VWjRyjbxx::getGmsfhm));
        DynamicDataSourceContextHolder.clearDataSourceType();
        return page.getRecords();
    }

    @Override
    public List<PoliceBasicInfo> getTargetData(int offset, int limit) {
        Page<PoliceBasicInfo> page = policeBasicInfoService.page(Page.of(offset, limit));
        return page.getRecords();
    }

    @Override
    public PoliceBasicInfo convert(VWjRyjbxx source) {
        return PoliceBasicInfoConvert.INSTANCE.convertPoliceBasicInfo(source);
    }

    @Override
    public Function<PoliceBasicInfo, String> getBusinessKeyGenerator() {
       return PoliceBasicInfo::getIdCard;
    }

    @Override
    public void batchInsert(List<PoliceBasicInfo> records) {
        policeBasicInfoService.saveBatch(records);
    }

    @Override
    public void batchUpdate(List<PoliceBasicInfo> records) {
        policeBasicInfoService.updateBatchById(records);
    }

    @Override
    public void batchDelete(List<String> businessKeys) {
        policeBasicInfoService.removeByIds(businessKeys);
    }

    @Override
    public long getSourceDataCount() {
        DynamicDataSourceContextHolder.setDataSourceType(String.valueOf(DataSourceType.DATASOURCE0));
        long count = vwjRyjbxxService.count();
        DynamicDataSourceContextHolder.clearDataSourceType();
        return count;
    }

    @Override
    public long getTargetDataCount() {
        return policeBasicInfoService.count();
    }
}
