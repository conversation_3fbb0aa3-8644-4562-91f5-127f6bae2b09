package com.hl.orasync.sync;

import java.util.List;
import java.util.function.Function;

public interface DataSyncProcessor<S, T> {
    /**
     * 获取源数据 - 支持分页
     */
    List<S> getSourceData(int offset, int limit);

    /**
     * 获取目标数据 - 支持分页
     */
    List<T> getTargetData(int offset, int limit);

    /**
     * 数据转换
     */
    T convert(S source);

    /**
     * 获取业务键生成器
     */
    Function<T, String> getBusinessKeyGenerator();

    /**
     * 批量插入
     */
    void batchInsert(List<T> records);

    /**
     * 批量更新
     */
    void batchUpdate(List<T> records);

    /**
     * 批量删除
     */
    void batchDelete(List<String> businessKeys);

    /**
     * 获取源数据总数
     */
    long getSourceDataCount();

    /**
     * 获取目标数据总数
     */
    long getTargetDataCount();
}