<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.orasync.mapper.VWjRyjbxxMapper">
  <resultMap id="BaseResultMap" type="com.hl.orasync.domain.VWjRyjbxx">
    <!--@mbg.generated-->
    <!--@Table ZTJC.V_WJ_RYJBXX-->
    <result column="ZP" jdbcType="VARCHAR" property="zp" />
    <result column="XM" jdbcType="VARCHAR" property="xm" />
    <result column="GMSFHM" jdbcType="VARCHAR" property="gmsfhm" />
    <result column="XB" jdbcType="VARCHAR" property="xb" />
    <result column="CSRQ" jdbcType="VARCHAR" property="csrq" />
    <result column="JG" jdbcType="VARCHAR" property="jg" />
    <result column="MZ" jdbcType="VARCHAR" property="mz" />
    <result column="JH" jdbcType="VARCHAR" property="jh" />
    <result column="DWMC" jdbcType="VARCHAR" property="dwmc" />
    <result column="BM" jdbcType="VARCHAR" property="bm" />
    <result column="LDZWCJ" jdbcType="VARCHAR" property="ldzwcj" />
    <result column="CJGAGZRQ" jdbcType="VARCHAR" property="cjgagzrq" />
    <result column="CJGZRQ" jdbcType="VARCHAR" property="cjgzrq" />
    <result column="SG" jdbcType="VARCHAR" property="sg" />
    <result column="TZ" jdbcType="VARCHAR" property="tz" />
    <result column="XX" jdbcType="VARCHAR" property="xx" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    ZP, XM, GMSFHM, XB, CSRQ, JG, MZ, JH, DWMC, BM, LDZWCJ, CJGAGZRQ, CJGZRQ, SG, TZ, 
    XX
  </sql>
</mapper>