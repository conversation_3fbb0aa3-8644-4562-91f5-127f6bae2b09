<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hl.archive.mapper.PoliceBasicInfoMapper">

  <resultMap id="PoliceStatisticsDTOMap" type="com.hl.archive.domain.dto.PoliceStatisticsDTO">
    <result column="department" property="department" />
    <result column="total_count" property="totalCount" />
    <result column="age_20_30" property="age20To30" />
    <result column="age_30_40" property="age30To40" />
    <result column="age_40_50" property="age40To50" />
    <result column="age_50_up" property="age50Up" />
    <result column="edu_zhuan" property="eduZhuan" />
    <result column="edu_ben" property="eduBen" />
    <result column="edu_shuo" property="eduShuo" />
    <result column="edu_boshi" property="eduBoshi" />
    <result column="avg_age" property="avgAge" />
    <result column="male_count" property="maleCount" />
    <result column="female_count" property="femaleCount" />
    <result column="on_duty_count" property="onDutyCount" />
    <result column="off_duty_count" property="offDutyCount" />
  </resultMap>

  <select id="getPoliceStatisticsByDepartment" resultMap="PoliceStatisticsDTOMap">
    SELECT
      CONCAT( SUBSTR(organization_id,1,8),'0000') department,
      COUNT(*) AS total_count,
      SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) BETWEEN 20 AND 30 THEN 1 ELSE 0 END) AS age_20_30,
      SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 30 AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= 40 THEN 1 ELSE 0 END) AS age_30_40,
    SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 40 AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= 50 THEN 1 ELSE 0 END) AS age_40_50,
    SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 50 THEN 1 ELSE 0 END) AS age_50_up,
    SUM(CASE WHEN education_level LIKE '%专科%' OR education_level LIKE '%大专%' THEN 1 ELSE 0 END) AS edu_zhuan,
    SUM(CASE WHEN education_level LIKE '%硕士%' THEN 1 ELSE 0 END) AS edu_shuo,
    SUM(CASE WHEN education_level LIKE '%博士%' THEN 1 ELSE 0 END) AS edu_boshi,
    SUM(CASE WHEN education_level LIKE '%大学%' THEN 1 ELSE 0 END) AS edu_ben,
    ROUND(AVG(TIMESTAMPDIFF(YEAR, birth_date, CURDATE())), 2) AS avg_age,
    SUM(CASE WHEN gender = 1 THEN 1 ELSE 0 END) AS male_count,
    SUM(CASE WHEN gender = 2 THEN 1 ELSE 0 END) AS female_count,
    SUM(CASE WHEN duty_status = 0 THEN 1 ELSE 0 END) AS on_duty_count,
    SUM(CASE WHEN duty_status != 0 OR duty_status IS NULL THEN 1 ELSE 0 END) AS off_duty_count
    FROM police_basic_info
    WHERE is_deleted = 0
    GROUP BY CONCAT(SUBSTR(organization_id,1,8),'0000')
  </select>

  <select id="getPoliceStatisticsByOrgId" resultMap="PoliceStatisticsDTOMap">
    <choose>
      <when test="organizationId != null and organizationId == '320412000000'">
        <!-- 当organizationId为320412000000时，统计全部数据 -->
        SELECT
          '320412000000' as department,
          COUNT(*) AS total_count,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) BETWEEN 20 AND 30 THEN 1 ELSE 0 END) AS age_20_30,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 30 AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= 40 THEN 1 ELSE 0 END) AS age_30_40,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 40 AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= 50 THEN 1 ELSE 0 END) AS age_40_50,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 50 THEN 1 ELSE 0 END) AS age_50_up,
          SUM(CASE WHEN education_level LIKE '%专科%' OR education_level LIKE '%大专%' THEN 1 ELSE 0 END) AS edu_zhuan,
          SUM(CASE WHEN education_level LIKE '%硕士%' THEN 1 ELSE 0 END) AS edu_shuo,
          SUM(CASE WHEN education_level LIKE '%博士%' THEN 1 ELSE 0 END) AS edu_boshi,
          SUM(CASE WHEN education_level LIKE '%大学%' THEN 1 ELSE 0 END) AS edu_ben,
          ROUND(AVG(TIMESTAMPDIFF(YEAR, birth_date, CURDATE())), 2) AS avg_age,
          SUM(CASE WHEN gender = 1 THEN 1 ELSE 0 END) AS male_count,
          SUM(CASE WHEN gender = 2 THEN 1 ELSE 0 END) AS female_count,
          SUM(CASE WHEN duty_status = 0 THEN 1 ELSE 0 END) AS on_duty_count,
          SUM(CASE WHEN duty_status != 0 OR duty_status IS NULL THEN 1 ELSE 0 END) AS off_duty_count
        FROM police_basic_info
        WHERE is_deleted = 0
      </when>
      <otherwise>
        <!-- 其他情况按组织查询 -->
        SELECT
          CONCAT( SUBSTR(organization_id,1,8),'0000') department,
          COUNT(*) AS total_count,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) BETWEEN 20 AND 30 THEN 1 ELSE 0 END) AS age_20_30,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 30 AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= 40 THEN 1 ELSE 0 END) AS age_30_40,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 40 AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= 50 THEN 1 ELSE 0 END) AS age_40_50,
          SUM(CASE WHEN TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 50 THEN 1 ELSE 0 END) AS age_50_up,
          SUM(CASE WHEN education_level LIKE '%专科%' OR education_level LIKE '%大专%' THEN 1 ELSE 0 END) AS edu_zhuan,
          SUM(CASE WHEN education_level LIKE '%硕士%' THEN 1 ELSE 0 END) AS edu_shuo,
          SUM(CASE WHEN education_level LIKE '%博士%' THEN 1 ELSE 0 END) AS edu_boshi,
          SUM(CASE WHEN education_level LIKE '%大学%' THEN 1 ELSE 0 END) AS edu_ben,
          ROUND(AVG(TIMESTAMPDIFF(YEAR, birth_date, CURDATE())), 2) AS avg_age,
          SUM(CASE WHEN gender = 1 THEN 1 ELSE 0 END) AS male_count,
          SUM(CASE WHEN gender = 2 THEN 1 ELSE 0 END) AS female_count,
          SUM(CASE WHEN duty_status = 0 THEN 1 ELSE 0 END) AS on_duty_count,
          SUM(CASE WHEN duty_status != 0 OR duty_status IS NULL THEN 1 ELSE 0 END) AS off_duty_count
        FROM police_basic_info
        WHERE is_deleted = 0
          <if test="organizationId != null and organizationId != ''">
            AND organization_id LIKE CONCAT(#{organizationId}, '%')
          </if>
        GROUP BY CONCAT(SUBSTR(organization_id,1,8),'0000') limit 1
      </otherwise>
    </choose>
  </select>

  <!-- 统计数字穿透查询 - 分页查询人员基本信息 -->
  <select id="getPoliceListByStatisticsType" resultType="com.hl.archive.domain.entity.PoliceBasicInfo">
    SELECT *
    FROM police_basic_info
    WHERE is_deleted = 0
      <!-- 组织ID处理 -->
      <choose>
        <when test="request.organizationId != null and request.organizationId == '320412000000'">
          <!-- 当organizationId为320412000000时，查询全部数据，不添加组织过滤条件 -->
        </when>
        <when test="request.organizationId != null and request.organizationId != ''">
          AND organization_id LIKE CONCAT(#{request.organizationId}, '%')
        </when>
      </choose>

    <if test="request.name != null and request.name != ''">
      AND name LIKE CONCAT('%', #{request.name}, '%')
    </if>

      <!-- 优先使用自由组合条件，如果没有则使用statisticsType -->
      <choose>
        <when test="request.educationLevel != null or request.gender != null or request.minAge != null or request.maxAge != null or request.unitName != null or request.department != null or request.politicalIdentity != null or request.positionName != null or request.dutyStatus != null">
          <!-- 自由组合查询条件 -->
          <if test="request.educationLevel != null and request.educationLevel != ''">
            AND education_level LIKE CONCAT('%', #{request.educationLevel}, '%')
          </if>
          <if test="request.gender != null">
            AND gender = #{request.gender}
          </if>
          <if test="request.minAge != null and request.maxAge != null">
            AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) BETWEEN #{request.minAge} AND #{request.maxAge}
          </if>
          <if test="request.minAge != null and request.maxAge == null">
            AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &gt;= #{request.minAge}
          </if>
          <if test="request.minAge == null and request.maxAge != null">
            AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= #{request.maxAge}
          </if>
          <if test="request.unitName != null and request.unitName != ''">
            AND unit_name LIKE CONCAT('%', #{request.unitName}, '%')
          </if>
          <if test="request.department != null and request.department != ''">
            AND department LIKE CONCAT('%', #{request.department}, '%')
          </if>
          <if test="request.politicalIdentity != null and request.politicalIdentity != ''">
            AND political_identity LIKE CONCAT('%', #{request.politicalIdentity}, '%')
          </if>
          <if test="request.positionName != null and request.positionName != ''">
            AND position_name LIKE CONCAT('%', #{request.positionName}, '%')
          </if>
          <if test="request.dutyStatus != null">
            <choose>
              <when test="request.dutyStatus == 0">
                AND duty_status = 0
              </when>
              <otherwise>
                AND (duty_status != 0 OR duty_status IS NULL)
              </otherwise>
            </choose>
          </if>
        </when>
        <otherwise>
          <!-- 使用statisticsType进行单一条件查询 -->
          <choose>
            <when test="request.statisticsType == 'age_20_30' or request.statisticsType == 'age20To30'">
              AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) BETWEEN 20 AND 30
            </when>
            <when test="request.statisticsType == 'age_30_40' or request.statisticsType == 'age30To40'">
              AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 30 AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= 40
            </when>
            <when test="request.statisticsType == 'age_40_50' or request.statisticsType == 'age40To50'">
              AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 40 AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) &lt;= 50
            </when>
            <when test="request.statisticsType == 'age_50_up' or request.statisticsType == 'age50Up'">
              AND TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) > 50
            </when>
            <when test="request.statisticsType == 'edu_zhuan' or request.statisticsType == 'eduZhuan'">
              AND (education_level LIKE '%专科%' OR education_level LIKE '%大专%')
            </when>
            <when test="request.statisticsType == 'edu_ben' or request.statisticsType == 'eduBen'">
              AND education_level LIKE '%大学%'
            </when>
            <when test="request.statisticsType == 'edu_shuo' or request.statisticsType == 'eduShuo'">
              AND education_level LIKE '%硕士%'
            </when>
            <when test="request.statisticsType == 'edu_boshi' or request.statisticsType == 'eduBoshi'">
              AND education_level LIKE '%博士%'
            </when>
            <when test="request.statisticsType == 'maleCount'">
              AND gender = 1
            </when>
            <when test="request.statisticsType == 'femaleCount'">
              AND gender = 2
            </when>
            <when test="request.statisticsType == 'onDutyCount'">
              AND duty_status = 0
            </when>
            <when test="request.statisticsType == 'offDutyCount'">
              AND (duty_status != 0 OR duty_status IS NULL)
            </when>
          </choose>
        </otherwise>
      </choose>
    ORDER BY created_at DESC
  </select>

</mapper>