# 项目相关配置
hualong:
  # title
  title: 民警全息档案
  # 描述
  description: api
  # 版本
  version: 0.0.1
  # 返回内容包含字段: 成功码
  success: 200
  # 返回最大字段长度
  logMaxLen: 1000
  # corePoolSize
  corePoolSize: 5

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 28183
  # GRACEFUL (优雅)	当应用程序以"GRACEFUL"模式关闭时，它不会接受新的请求且会尝试完成所有正在进行的请求和处理，然后才会终止。
  # IMMEDIATE( 立即)	当应用程序以"IMMEDIATE"模式关闭时，它会立即终止，而不管当前是否有任何活动任务或请求。
  shutdown: graceful
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.hl: debug
    org.springframework: info
  config: ./conf/log4j2.xml
  # 所有的请求日志，基于aop的写入日志进入mysql
  mysql:
    # 默认关闭
    enabled: true
    # 使用的数据源
    datasource: MASTER
    # 写入的数据库
    database: user_log
    # 日志的最大存储时长，单位天
    maxSaveDays: 90
  # 所有的请求日志，基于aop的写入日志进入Slf4j
  slf4j:
    # 默认关闭
    enabled: true
    filterUrl: /person,/sso,/export_dem1
  logSql:
    # 默认开启, 总开关
    enabled: true
    # 是否开启，注解的打印, 默认true
    annotate: true
    # 是否开启所有sql的打印, 默认关闭
    all: true

# Spring配置
spring:
  main:
    banner-mode: off
    allow-bean-definition-overriding: true
    # servlet, none,(Servlet、Reactive)
#    web-application-type: none
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  # 模板引擎
  thymeleaf:
    mode: HTML
    encoding: utf-8
    # 禁用缓存
    cache: false
    prefix: file:./templates/
    check-template-location: false
  profiles:
    active: druid
#    activea:
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  10MB
       # 设置总上传的文件大小
       max-request-size:  20MB
  application:
    name: hl-wj-police-archive
  cloud:
    nacos:
      discovery:
        server-addr: **************:8848
        group: default
        namespace: cz-wj
        username: nacos
        password: hl123
      config:
        server-addr: **************:8848
        group: default
        namespace: cz-wj
        username: nacos
        password: hl123
  redis:
    host: *************  # 地址
    port: 10016  # 端口号
    database: 1  # 数据库索引（默认为0）
    timeout: 5000  # 连接超时时间（毫秒）
    lettuce:
      pool:
        max-active: 20  # 连接池最大连接数（使用负值表示没有限制）
        max-wait: -1  # 最大阻塞等待时间（负数表示没有限制）
        max-idle: 5  # 连接池中最大空闲连接
        min-idle: 0  # 连接池中最小空闲连接
  security:
    # 未认证是，返回的错误码，默认401
    # unauthorized: 401
    # 在header中token名称
    type: ssork
    passToken:
    sso:
      # token: token
      # sso认证地址
      url: http://*************:8184/
      # project_id
      projectId: jzzl
      # sso中项目访问的token
      projectToken: 56as4da344dc7cc4f57909fd5264588d
  rabbitmq:
    host: **************
    port: 5672
    username: admin
    password: admin
    virtual-host: /
    listener:
      simple:
        acknowledge-mode: manual

# MyBatis配置
mybatis:
    # 是否开启
    # druidEnabled: true
    printSql: false
    # 搜索指定包别名
    typeAliasesPackage: com.hl.**.domain
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath*:mapper/**/*Mapper.xml
    # 加载全局的配置文件
    configLocation: classpath:mybatis/mybatis-config.xml

mybatis-plus:
  global-config:
    banner: off
#  configuration:
#    map-underscore-to-camel-case: false

# PageHelper分页插件
pagehelper: 
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql 

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀


# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*


snail-job:
  enabled: false
  # 任务调度服务器信息
  server:
    # 服务器IP地址（或域名）；集群时建议通过 nginx 做负载均衡
    host: *************
    # 服务器通讯端口（不是后台管理页面服务端口）
    port: 37889
  # 命名空间 【上面配置的空间的唯一标识】
  namespace: _SiGT0a16dpUdOOo99vkgHYJVMbBO7tx
  # 接入组名【上面配置的组名称】注意: 若通过注解配置了这里的配置不生效
  group: test_notify_group
  # 接入组 token 【上面配置的token信息】
  token: SJ_Wyz3dmsdbDOkDujOTSSoBjGQP1BMsVnj
  # 客户端绑定IP，必须服务器可以访问到；默认自动推断，在服务器无法调度客户端时需要手动配置
  port: 17990
  rpc-type: grpc
  host: *************

easy-es:
  compatible: true # 兼容模式开关,默认为false,若您的es客户端版本小于8.x,务必设置为true才可正常使用,8.x及以上则可忽略此项配置
  enable: true #默认为true,若为false则认为不启用本框架
  address : *************:9200 # es的连接地址,必须含端口 若为集群,则可以用逗号隔开 例如:127.0.0.1:9200,*********:9200

# 搜索功能配置
search:
  enabled: true # 是否启用搜索功能
  sync:
    enabled: false # 是否启用定时同步，生产环境建议设置为 true
    batchSize: 1000 # 批量同步大小
    timeout: 300 # 同步超时时间（秒）
    autoSyncOnStartup: false # 是否在启动时自动同步
  query:
    defaultPageSize: 20 # 默认页大小
    maxPageSize: 100 # 最大页大小
    defaultFuzziness: 2 # 默认模糊度
    timeout: 5000 # 搜索超时时间（毫秒）
    cacheEnabled: true # 是否启用缓存
    cacheExpireTime: 300 # 缓存过期时间（秒）
  highlight:
    preTag: "<em class=\"highlight\">" # 高亮前缀标签
    postTag: "</em>" # 高亮后缀标签
    fragmentSize: 100 # 高亮片段大小
    numberOfFragments: 3 # 高亮片段数量

