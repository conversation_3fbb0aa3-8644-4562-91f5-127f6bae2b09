package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjQtQtsx;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-14T15:28:42+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceOtherMattersToVWjQtQtsxMapperImpl implements PoliceOtherMattersToVWjQtQtsxMapper {

    @Override
    public VWjQtQtsx convert(PoliceOtherMatters source) {
        if ( source == null ) {
            return null;
        }

        VWjQtQtsx vWjQtQtsx = new VWjQtQtsx();

        vWjQtQtsx.setGmsfhm( source.getIdCard() );
        vWjQtQtsx.setBz( source.getReportContent() );
        if ( source.getRegistrationDate() != null ) {
            vWjQtQtsx.setDjrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getRegistrationDate() ) );
        }

        return vWjQtQtsx;
    }

    @Override
    public VWjQtQtsx convert(PoliceOtherMatters source, VWjQtQtsx target) {
        if ( source == null ) {
            return target;
        }

        target.setGmsfhm( source.getIdCard() );
        target.setBz( source.getReportContent() );
        if ( source.getRegistrationDate() != null ) {
            target.setDjrq( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getRegistrationDate() ) );
        }
        else {
            target.setDjrq( null );
        }

        return target;
    }
}
