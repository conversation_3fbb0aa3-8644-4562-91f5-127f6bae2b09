package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjZnShkbjg;
import java.time.format.DateTimeFormatter;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-14T15:28:42+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceFamilyPaidInstitutionsToVWjZnShkbjgMapperImpl implements PoliceFamilyPaidInstitutionsToVWjZnShkbjgMapper {

    @Override
    public VWjZnShkbjg convert(PoliceFamilyPaidInstitutions source) {
        if ( source == null ) {
            return null;
        }

        VWjZnShkbjg vWjZnShkbjg = new VWjZnShkbjg();

        vWjZnShkbjg.setJyfw( source.getBusinessScope() );
        vWjZnShkbjg.setJyd( source.getBusinessAddress() );
        vWjZnShkbjg.setQylxmc( source.getInstitutionType() );
        vWjZnShkbjg.setGmsfhm( source.getIdCard() );
        vWjZnShkbjg.setXmFr( source.getName() );
        if ( source.getRegisteredCapital() != null ) {
            vWjZnShkbjg.setZczb( source.getRegisteredCapital().toString() );
        }
        vWjZnShkbjg.setZcd( source.getRegistrationAddress() );
        vWjZnShkbjg.setQymc( source.getInstitutionName() );
        vWjZnShkbjg.setZch( source.getSocialCreditCode() );
        if ( source.getEstablishmentDate() != null ) {
            vWjZnShkbjg.setClsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getEstablishmentDate() ) );
        }

        return vWjZnShkbjg;
    }

    @Override
    public VWjZnShkbjg convert(PoliceFamilyPaidInstitutions source, VWjZnShkbjg target) {
        if ( source == null ) {
            return target;
        }

        target.setJyfw( source.getBusinessScope() );
        target.setJyd( source.getBusinessAddress() );
        target.setQylxmc( source.getInstitutionType() );
        target.setGmsfhm( source.getIdCard() );
        target.setXmFr( source.getName() );
        if ( source.getRegisteredCapital() != null ) {
            target.setZczb( source.getRegisteredCapital().toString() );
        }
        else {
            target.setZczb( null );
        }
        target.setZcd( source.getRegistrationAddress() );
        target.setQymc( source.getInstitutionName() );
        target.setZch( source.getSocialCreditCode() );
        if ( source.getEstablishmentDate() != null ) {
            target.setClsj( DateTimeFormatter.ISO_LOCAL_DATE_TIME.format( source.getEstablishmentDate() ) );
        }
        else {
            target.setClsj( null );
        }

        return target;
    }
}
