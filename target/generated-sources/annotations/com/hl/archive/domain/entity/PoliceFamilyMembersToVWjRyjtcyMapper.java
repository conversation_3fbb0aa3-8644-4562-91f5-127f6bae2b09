package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjRyjtcy;
import com.hl.orasync.domain.VWjRyjtcyToPoliceFamilyMembersMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__351;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__351.class,
    uses = {ConversionUtils.class,VWjRyjtcyToPoliceFamilyMembersMapper.class},
    imports = {}
)
public interface PoliceFamilyMembersToVWjRyjtcyMapper extends BaseMapper<PoliceFamilyMembers, VWjRyjtcy> {
  @Mapping(
      target = "csrq",
      source = "birthDate"
  )
  @Mapping(
      target = "xm",
      source = "memberName"
  )
  @Mapping(
      target = "gzdw",
      source = "workUnitPosition"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "zzmm",
      source = "politicalStatus"
  )
  @Mapping(
      target = "sjhm",
      source = "mobilePhone"
  )
  @Mapping(
      target = "rygx",
      source = "relationship"
  )
  VWjRyjtcy convert(PoliceFamilyMembers source);

  @Mapping(
      target = "csrq",
      source = "birthDate"
  )
  @Mapping(
      target = "xm",
      source = "memberName"
  )
  @Mapping(
      target = "gzdw",
      source = "workUnitPosition"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "zzmm",
      source = "politicalStatus"
  )
  @Mapping(
      target = "sjhm",
      source = "mobilePhone"
  )
  @Mapping(
      target = "rygx",
      source = "relationship"
  )
  VWjRyjtcy convert(PoliceFamilyMembers source, @MappingTarget VWjRyjtcy target);
}
