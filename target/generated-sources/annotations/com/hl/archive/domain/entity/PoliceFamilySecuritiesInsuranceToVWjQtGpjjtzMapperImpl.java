package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjQtGpjjtz;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-14T15:28:42+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class PoliceFamilySecuritiesInsuranceToVWjQtGpjjtzMapperImpl implements PoliceFamilySecuritiesInsuranceToVWjQtGpjjtzMapper {

    @Override
    public VWjQtGpjjtz convert(PoliceFamilySecuritiesInsurance source) {
        if ( source == null ) {
            return null;
        }

        VWjQtGpjjtz vWjQtGpjjtz = new VWjQtGpjjtz();

        vWjQtGpjjtz.setRjz( source.getNetValuePremium() );
        vWjQtGpjjtz.setGmsfhm( source.getIdCard() );
        vWjQtGpjjtz.setSl( source.getHoldingQuantity() );
        vWjQtGpjjtz.setXmCyr( source.getHolderName() );
        vWjQtGpjjtz.setMcdm( source.getSecurityNameCode() );

        return vWjQtGpjjtz;
    }

    @Override
    public VWjQtGpjjtz convert(PoliceFamilySecuritiesInsurance source, VWjQtGpjjtz target) {
        if ( source == null ) {
            return target;
        }

        target.setRjz( source.getNetValuePremium() );
        target.setGmsfhm( source.getIdCard() );
        target.setSl( source.getHoldingQuantity() );
        target.setXmCyr( source.getHolderName() );
        target.setMcdm( source.getSecurityNameCode() );

        return target;
    }
}
