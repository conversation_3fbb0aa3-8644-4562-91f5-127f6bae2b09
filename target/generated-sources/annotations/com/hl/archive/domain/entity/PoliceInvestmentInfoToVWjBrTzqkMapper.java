package com.hl.archive.domain.entity;

import com.hl.orasync.domain.VWjBrTzqk;
import com.hl.orasync.domain.VWjBrTzqkToPoliceInvestmentInfoMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__351;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__351.class,
    uses = {ConversionUtils.class,VWjBrTzqkToPoliceInvestmentInfoMapper.class},
    imports = {}
)
public interface PoliceInvestmentInfoToVWjBrTzqkMapper extends BaseMapper<PoliceInvestmentInfo, VWjBrTzqk> {
  @Mapping(
      target = "tzcpmc",
      source = "investmentEntity"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "tzsj",
      source = "transactionDate"
  )
  @Mapping(
      target = "tzqx",
      source = "investmentSource"
  )
  @Mapping(
      target = "je",
      source = "investmentAmount"
  )
  VWjBrTzqk convert(PoliceInvestmentInfo source);

  @Mapping(
      target = "tzcpmc",
      source = "investmentEntity"
  )
  @Mapping(
      target = "gmsfhm",
      source = "idCard"
  )
  @Mapping(
      target = "tzsj",
      source = "transactionDate"
  )
  @Mapping(
      target = "tzqx",
      source = "investmentSource"
  )
  @Mapping(
      target = "je",
      source = "investmentAmount"
  )
  VWjBrTzqk convert(PoliceInvestmentInfo source, @MappingTarget VWjBrTzqk target);
}
