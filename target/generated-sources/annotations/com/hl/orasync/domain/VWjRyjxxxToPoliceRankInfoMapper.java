package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceRankInfo;
import com.hl.archive.domain.entity.PoliceRankInfoToVWjRyjxxxMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__351;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__351.class,
    uses = {ConversionUtils.class,PoliceRankInfoToVWjRyjxxxMapper.class},
    imports = {}
)
public interface VWjRyjxxxToPoliceRankInfoMapper extends BaseMapper<VWjRyjxxx, PoliceRankInfo> {
  @Mapping(
      target = "rankType",
      source = "sxzl"
  )
  @Mapping(
      target = "promotionReason",
      source = "sxyy"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "rankEndDate",
      source = "xczzrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "promotionDate",
      source = "sxsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "rankStartDate",
      source = "xcqsrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "adminLevelAtPromotion",
      source = "sxsxzzj"
  )
  @Mapping(
      target = "rankTitle",
      source = "xc"
  )
  PoliceRankInfo convert(VWjRyjxxx source);

  @Mapping(
      target = "rankType",
      source = "sxzl"
  )
  @Mapping(
      target = "promotionReason",
      source = "sxyy"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "rankEndDate",
      source = "xczzrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "promotionDate",
      source = "sxsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "rankStartDate",
      source = "xcqsrq",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "adminLevelAtPromotion",
      source = "sxsxzzj"
  )
  @Mapping(
      target = "rankTitle",
      source = "xc"
  )
  PoliceRankInfo convert(VWjRyjxxx source, @MappingTarget PoliceRankInfo target);
}
