package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceTraining;
import com.hl.archive.domain.entity.PoliceTrainingToVWjRyjyxlMapper;
import com.hl.orasync.util.ConversionUtils;
import io.github.linpeilie.AutoMapperConfig__351;
import io.github.linpeilie.BaseMapper;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(
    config = AutoMapperConfig__351.class,
    uses = {ConversionUtils.class,PoliceTrainingToVWjRyjyxlMapper.class},
    imports = {}
)
public interface VWjRyjyxlToPoliceTrainingMapper extends BaseMapper<VWjRyjyxl, PoliceTraining> {
  @Mapping(
      target = "trainingEndDate",
      source = "pxzzsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "trainingStartDate",
      source = "pxqzsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "trainingName",
      source = "pxbmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "organizerName",
      source = "pxzbdwmc"
  )
  PoliceTraining convert(VWjRyjyxl source);

  @Mapping(
      target = "trainingEndDate",
      source = "pxzzsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "trainingStartDate",
      source = "pxqzsj",
      qualifiedByName = {"strToDate"}
  )
  @Mapping(
      target = "trainingName",
      source = "pxbmc"
  )
  @Mapping(
      target = "idCard",
      source = "gmsfhm"
  )
  @Mapping(
      target = "organizerName",
      source = "pxzbdwmc"
  )
  PoliceTraining convert(VWjRyjyxl source, @MappingTarget PoliceTraining target);
}
