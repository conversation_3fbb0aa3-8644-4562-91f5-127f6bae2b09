package com.hl.orasync.domain;

import com.hl.archive.domain.entity.PoliceFamilyOverseasMigration;
import com.hl.orasync.util.ConversionUtils;
import javax.annotation.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-14T15:28:42+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
@Component
public class VWjZnYjqkToPoliceFamilyOverseasMigrationMapperImpl implements VWjZnYjqkToPoliceFamilyOverseasMigrationMapper {

    @Override
    public PoliceFamilyOverseasMigration convert(VWjZnYjqk source) {
        if ( source == null ) {
            return null;
        }

        PoliceFamilyOverseasMigration policeFamilyOverseasMigration = new PoliceFamilyOverseasMigration();

        policeFamilyOverseasMigration.setMigrationCountry( source.getYjgj() );
        policeFamilyOverseasMigration.setIdCard( source.getGmsfhm() );
        policeFamilyOverseasMigration.setCurrentCity( source.getXjzcs() );
        policeFamilyOverseasMigration.setMigrationDocumentNumber( source.getYjzjhm() );
        policeFamilyOverseasMigration.setMigrationCategory( source.getYjlx() );
        policeFamilyOverseasMigration.setBasisDate( ConversionUtils.strToDate( source.getYjsj() ) );
        policeFamilyOverseasMigration.setRemarks( source.getBz() );
        policeFamilyOverseasMigration.setFamilyMemberName( source.getXmPozn() );

        return policeFamilyOverseasMigration;
    }

    @Override
    public PoliceFamilyOverseasMigration convert(VWjZnYjqk source, PoliceFamilyOverseasMigration target) {
        if ( source == null ) {
            return target;
        }

        target.setMigrationCountry( source.getYjgj() );
        target.setIdCard( source.getGmsfhm() );
        target.setCurrentCity( source.getXjzcs() );
        target.setMigrationDocumentNumber( source.getYjzjhm() );
        target.setMigrationCategory( source.getYjlx() );
        target.setBasisDate( ConversionUtils.strToDate( source.getYjsj() ) );
        target.setRemarks( source.getBz() );
        target.setFamilyMemberName( source.getXmPozn() );

        return target;
    }
}
