package com.hl.orasync.convert;

import com.hl.archive.domain.entity.PolicePositionRank;
import com.hl.orasync.domain.VWjRyzwzj;
import java.time.LocalDateTime;
import javax.annotation.Generated;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-14T15:28:41+0800",
    comments = "version: 1.5.5.Final, compiler: javac, environment: Java 1.8.0_432-432 (OpenLogic-OpenJDK)"
)
public class PolicePositionRankConvertImpl implements PolicePositionRankConvert {

    @Override
    public PolicePositionRank convertPolicePositionRank(VWjRyzwzj vwjRyzwzj) {
        if ( vwjRyzwzj == null ) {
            return null;
        }

        PolicePositionRank policePositionRank = new PolicePositionRank();

        policePositionRank.setPositionName( vwjRyzwzj.getZwmc() );
        policePositionRank.setPolicePositionLevel( vwjRyzwzj.getGazwjb() );
        if ( vwjRyzwzj.getZwsxsj() != null ) {
            policePositionRank.setCurrentPositionDate( LocalDateTime.parse( vwjRyzwzj.getZwsxsj() ) );
        }
        if ( vwjRyzwzj.getXzjsj() != null ) {
            policePositionRank.setCurrentRankDate( LocalDateTime.parse( vwjRyzwzj.getXzjsj() ) );
        }
        policePositionRank.setAppointmentDocument( vwjRyzwzj.getRzwh() );
        policePositionRank.setIdCard( vwjRyzwzj.getGmsfhm() );

        return policePositionRank;
    }
}
